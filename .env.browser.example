# Pinterest Authentication
PINTEREST_EMAIL=<EMAIL>
PINTEREST_PASSWORD=your_password
PINTEREST_USERNAME=your_username  # Your Pinterest username for profile URL

# Browser Configuration
BROWSER_HEADLESS=false  # Set to true to run without visible browser
BROWSER_VIEWPORT_WIDTH=1280
BROWSER_VIEWPORT_HEIGHT=800
BROWSER_USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36

# UI Interaction Delays (in seconds)
ACTION_DELAY_MIN=2.0  # Minimum delay between general actions
ACTION_DELAY_MAX=5.0  # Maximum delay between general actions

# Scrolling Configuration
SCROLL_DELAY_MIN=1.0  # Minimum delay between scrolls
SCROLL_DELAY_MAX=3.0  # Maximum delay between scrolls
SCROLL_CHUNK_MIN=500  # Minimum pixels to scroll
SCROLL_CHUNK_MAX=800  # Maximum pixels to scroll
SCROLL_CHECK_TIMEOUT=30  # Maximum time (seconds) to wait for new content to load

# Image Download Configuration
DOWNLOAD_DELAY_MIN=3.0  # Minimum delay between image downloads
DOWNLOAD_DELAY_MAX=7.0  # Maximum delay between image downloads
DOWNLOAD_TIMEOUT=30  # Maximum time (seconds) to wait for image download
PINTEREST_IMAGE_SIZE=original  # Options: 'original', '600x', '236x'

# Rate Limiting
MAX_PINS_PER_MINUTE=0  # Set to 0 for unlimited, or a number like 10 to limit rate
MAX_RETRIES=3  # Maximum number of retries for failed requests
RETRY_DELAY=5  # Delay (seconds) between retries

# Output Configuration
PINTEREST_OUTPUT_DIR=pinterest_export
SAVE_FAILED_PINS=true  # Whether to save information about failed pin downloads
ORGANIZE_BY_BOARD=false  # Whether to organize pins in subdirectories by board name

# Selectors (only change if Pinterest's HTML structure changes)
PIN_SELECTOR=[data-test-id="pin"]
PIN_TITLE_SELECTOR=[data-test-id="pin-title"]
PIN_IMAGE_SELECTOR=img[src*="pinimg.com"]
LOGIN_FORM_SELECTOR=[data-test-id="registerFormSubmitButton"]
HOME_SELECTOR=[data-test-id="user-home"] 