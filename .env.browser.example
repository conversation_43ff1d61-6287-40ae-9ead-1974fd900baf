# Pinterest Browser Exporter Configuration
# Copy this file to .env.browser and fill in your actual values

# =============================================================================
# AUTHENTICATION SETTINGS (Required)
# =============================================================================
PINTEREST_EMAIL=<EMAIL>
PINTEREST_PASSWORD=your_password
PINTEREST_USERNAME=your_username  # Your Pinterest username (from profile URL)

# =============================================================================
# BROWSER CONFIGURATION
# =============================================================================
BROWSER_HEADLESS=false  # Set to true to run without visible browser window
BROWSER_VIEWPORT_WIDTH=1280  # Browser window width (affects content loading)
BROWSER_VIEWPORT_HEIGHT=800  # Browser window height
BROWSER_USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36

# =============================================================================
# RATE LIMITING SETTINGS (Important for avoiding blocks)
# =============================================================================
# General action delays (login, navigation, etc.)
ACTION_DELAY_MIN=2.0  # Minimum delay between actions
ACTION_DELAY_MAX=5.0  # Maximum delay between actions

# Scrolling delays (affects how fast pages are scrolled)
SCROLL_DELAY_MIN=1.0  # Minimum delay between scrolls
SCROLL_DELAY_MAX=3.0  # Maximum delay between scrolls
SCROLL_CHECK_TIMEOUT=600  # Max time to wait for new content (10 minutes)
SCROLL_PROGRESS_INTERVAL=30  # How often to log progress (seconds)

# Random pause settings (adds human-like behavior)
SCROLL_PAUSE_CHANCE=0.1  # 10% chance of random pause
SCROLL_PAUSE_MIN=1.5  # Minimum pause duration
SCROLL_PAUSE_MAX=3.0  # Maximum pause duration

# Image download delays
DOWNLOAD_DELAY_MIN=3.0  # Minimum delay between image downloads
DOWNLOAD_DELAY_MAX=7.0  # Maximum delay between image downloads
DOWNLOAD_TIMEOUT=30  # Maximum time to wait for image download

# Overall rate limiting
MAX_PINS_PER_MINUTE=10  # Limit pins processed per minute (0 = no limit)
MAX_RETRIES=3  # Maximum retries for failed operations
RETRY_DELAY=5  # Delay between retries (seconds)

# =============================================================================
# OUTPUT CONFIGURATION
# =============================================================================
PINTEREST_OUTPUT_DIR=pinterest_export  # Directory for all output files
PINTEREST_IMAGE_SIZE=original  # Image quality: 'original', '600x', '236x'
SAVE_FAILED_PINS=true  # Save info about failed downloads
ORGANIZE_BY_BOARD=true  # Organize images in board subdirectories

# =============================================================================
# ADVANCED SETTINGS (Usually don't need to change)
# =============================================================================
# CSS selectors for Pinterest elements (only change if Pinterest updates)
PIN_SELECTOR=[data-test-id="pin"]
PIN_TITLE_SELECTOR=[data-test-id="pin-title"]
PIN_IMAGE_SELECTOR=img[src*="pinimg.com"]
LOGIN_FORM_SELECTOR=[data-test-id="registerFormSubmitButton"]
HOME_SELECTOR=[data-test-id="user-home"]

# =============================================================================
# CONFIGURATION TIPS
# =============================================================================
#
# GETTING STARTED:
# 1. Copy this file to .env.browser
# 2. Fill in your Pinterest email, password, and username
# 3. Start with default settings, adjust if needed
#
# RATE LIMITING ADVICE:
# - Start conservative (higher delays, lower pins per minute)
# - If you get blocked, increase delays and reduce rate
# - Pinterest may temporarily block aggressive scraping
#
# PERFORMANCE TUNING:
# - Use BROWSER_HEADLESS=true for better performance
# - Increase MAX_PINS_PER_MINUTE if not getting blocked
# - Use 'original' for best quality, '600x' for balance
#
# TROUBLESHOOTING:
# - Check pinterest_export.log for detailed error info
# - Try headless mode if browser crashes
# - Reduce rate if getting blocked or errors