# Pinterest Browser Exporter - Detailed Documentation

## Table of Contents
1. [Overview](#overview)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Command Reference](#command-reference)
5. [Technical Details](#technical-details)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Usage](#advanced-usage)

## Overview

The Pinterest Browser Exporter is a sophisticated web scraping tool that automates the process of downloading your Pinterest boards and pins. Unlike API-based solutions, this tool uses browser automation to access Pinterest directly, bypassing API limitations and rate restrictions.

### Key Features
- **Browser Automation**: Uses Selenium WebDriver for reliable Pinterest interaction
- **Smart Scrolling**: Handles Pinterest's infinite scroll with duplicate detection
- **Resume Support**: Automatically resumes interrupted downloads
- **Rate Limiting**: Configurable delays to avoid detection
- **Progress Tracking**: Detailed logging and progress monitoring
- **Multiple Formats**: Saves both images and comprehensive metadata
- **Report Generation**: Creates HTML galleries of your collections

## Installation

### Prerequisites
- Python 3.7 or higher
- Google Chrome or Chromium browser
- Stable internet connection

### Step-by-Step Installation

1. **<PERSON>lone or Download the Repository**
   ```bash
   git clone <repository-url>
   cd pinterest-export
   ```

2. **Install Python Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify Chrome Installation**
   The script will automatically download ChromeDriver, but ensure Chrome is installed:
   - Windows: Download from Google Chrome website
   - macOS: `brew install --cask google-chrome`
   - Linux: `sudo apt-get install google-chrome-stable`

## Configuration

### Environment File Setup

Create `.env.browser` from the example:
```bash
cp .env.browser.example .env.browser
```

### Configuration Categories

#### Authentication Settings
```env
PINTEREST_EMAIL=<EMAIL>
PINTEREST_PASSWORD=your-password
PINTEREST_USERNAME=your-username
```

#### Browser Configuration
```env
BROWSER_HEADLESS=false              # true = run in background
BROWSER_VIEWPORT_WIDTH=1280         # Browser window width
BROWSER_VIEWPORT_HEIGHT=800         # Browser window height
```

#### Rate Limiting (Important for avoiding blocks)
```env
ACTION_DELAY_MIN=2.0               # Min delay between actions
ACTION_DELAY_MAX=5.0               # Max delay between actions
SCROLL_DELAY_MIN=1.0               # Min delay between scrolls
SCROLL_DELAY_MAX=3.0               # Max delay between scrolls
DOWNLOAD_DELAY_MIN=3.0             # Min delay between downloads
DOWNLOAD_DELAY_MAX=7.0             # Max delay between downloads
MAX_PINS_PER_MINUTE=10             # Rate limit (0 = no limit)
```

#### Output Settings
```env
PINTEREST_OUTPUT_DIR=pinterest_export
PINTEREST_IMAGE_SIZE=original       # original, 600x, or 236x
SAVE_FAILED_PINS=true
```

## Command Reference

### Basic Commands

#### Export All Boards
```bash
python pinterest_browser_export.py
```
Downloads all accessible boards and their pins.

#### Export Specific Boards
```bash
python pinterest_browser_export.py --boards board1,board2,board3
```
Use board slugs (URL-friendly names) separated by commas.

#### Skip Boards
```bash
python pinterest_browser_export.py --skip unwanted-board,another-board
```
Export all boards except the specified ones.

### Maintenance Commands

#### Re-crawl a Board
```bash
python pinterest_browser_export.py --recrawl board-slug
```
Updates an existing board's data, useful for adding new pins.

#### Rescan for Missing Data
```bash
# Populate missing titles
python pinterest_browser_export.py --rescan-titles

# Populate missing descriptions
python pinterest_browser_export.py --rescan-descriptions
```

### Analysis Commands

#### Generate Statistics
```bash
python pinterest_browser_export.py --stats
```
Shows download statistics, board counts, and storage usage.

#### Compare Counts
```bash
python pinterest_browser_export.py --compare-counts
```
Compares remote Pinterest counts with local downloads.

#### Check Missing Images
```bash
python pinterest_browser_export.py --check-missing
```
Identifies pins with missing image files.

### Report Generation

#### HTML Gallery Reports
```bash
# Generate reports for all boards
python pinterest_browser_export.py --html-reports

# Generate reports for specific boards
python pinterest_browser_export.py --html-reports --boards art,design
```

#### Video Pin Report
```bash
python pinterest_browser_export.py --video-report
```
Lists all video pins (which cannot be downloaded as images).

### Utility Commands

#### Sync Image Data
```bash
python pinterest_browser_export.py --sync-images
```
Updates JSON files to reflect actually downloaded images.

## Technical Details

### How It Works

1. **Browser Setup**: Launches Chrome with a persistent profile
2. **Authentication**: Logs into Pinterest using provided credentials
3. **Board Discovery**: Finds all accessible boards
4. **Pin Collection**: Scrolls through each board collecting pin URLs
5. **Metadata Extraction**: Visits each pin to extract detailed information
6. **Image Download**: Downloads high-quality images with proper naming
7. **Progress Tracking**: Saves progress to enable resuming

### File Structure

```
pinterest_export/
├── images/                    # Downloaded images
│   ├── board-slug-1/
│   │   ├── pin-id-1.jpg
│   │   └── pin-id-2.png
│   └── board-slug-2/
├── collections/              # JSON metadata
│   ├── board-slug-1.json
│   └── board-slug-2.json
├── reports/                  # HTML reports
│   ├── board-slug-1.html
│   └── index.html
├── chrome_profile/           # Browser profile data
├── download_progress.json    # Resume tracking
└── pinterest_export.log     # Detailed logs
```

### Data Format

Each board's JSON file contains:
```json
{
  "board_info": {
    "name": "Board Name",
    "slug": "board-slug",
    "url": "https://pinterest.com/user/board-slug/",
    "pin_count": 150
  },
  "pins": [
    {
      "id": "pin-id",
      "url": "https://pinterest.com/pin/pin-id/",
      "title": "Pin Title",
      "description": "Pin Description",
      "image_url": "https://i.pinimg.com/...",
      "local_image_path": "images/board-slug/pin-id.jpg",
      "board": "board-slug",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

## Troubleshooting

### Common Issues

#### Login Problems
- **Symptom**: "Login failed" error
- **Solutions**:
  - Verify credentials in `.env.browser`
  - Check for two-factor authentication (not supported)
  - Try logging in manually first in a regular browser

#### Browser Crashes
- **Symptom**: Browser closes unexpectedly
- **Solutions**:
  - Set `BROWSER_HEADLESS=true`
  - Reduce viewport size
  - Close other browser instances
  - Restart your computer

#### Rate Limiting
- **Symptom**: "Too many requests" or blocked access
- **Solutions**:
  - Increase delay values in configuration
  - Reduce `MAX_PINS_PER_MINUTE`
  - Wait before retrying
  - Use different IP address if possible

#### Missing Images
- **Symptom**: JSON files exist but images are missing
- **Solutions**:
  - Run `--check-missing` to identify issues
  - Check disk space
  - Verify internet connection
  - Re-run with `--recrawl board-name`

### Debug Mode

Enable detailed logging by setting the log level:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Performance Optimization

#### For Large Collections
- Use `BROWSER_HEADLESS=true` for better performance
- Increase `MAX_PINS_PER_MINUTE` if not getting blocked
- Run during off-peak hours
- Use SSD storage for better I/O performance

#### For Slow Connections
- Increase timeout values
- Reduce concurrent operations
- Use smaller image sizes (`PINTEREST_IMAGE_SIZE=600x`)

## Advanced Usage

### Custom Selectors

If Pinterest changes their HTML structure, update selectors in the code:
```python
self.selectors = {
    'pin_grid': '[data-test-id="board-feed"]',
    'pin_link': '[data-test-id="pin"] a',
    # ... other selectors
}
```

### Extending Functionality

The codebase is modular and can be extended:
- Add new metadata fields in `extract_pin_metadata()`
- Implement new export formats in `save_pins_data()`
- Add custom rate limiting strategies
- Integrate with cloud storage services

### Batch Processing

For multiple Pinterest accounts:
```bash
# Create separate config files
cp .env.browser .env.browser.account1
cp .env.browser .env.browser.account2

# Run with different configs
DOTENV_PATH=.env.browser.account1 python pinterest_browser_export.py
DOTENV_PATH=.env.browser.account2 python pinterest_browser_export.py
```

### Integration with Other Tools

The JSON output can be used with:
- **Data Analysis**: Import into pandas, R, or Excel
- **Web Galleries**: Use with static site generators
- **Backup Systems**: Integrate with cloud storage APIs
- **Search Engines**: Index with Elasticsearch or Solr

## Legal and Ethical Considerations

### Terms of Service
- This tool is for personal use only
- Respect Pinterest's Terms of Service
- Don't redistribute downloaded content without permission
- Be mindful of copyright and intellectual property rights

### Rate Limiting Ethics
- Use reasonable delays to avoid overloading Pinterest's servers
- Don't run multiple instances simultaneously
- Consider Pinterest's resources and other users

### Data Privacy
- Keep your credentials secure
- Don't share downloaded personal data
- Be aware of privacy implications when sharing boards

## Support and Contributing

### Getting Help
1. Check this documentation first
2. Review the troubleshooting section
3. Check existing GitHub issues
4. Create a new issue with detailed information

### Contributing
- Fork the repository
- Create a feature branch
- Add tests for new functionality
- Submit a pull request with clear description

### Reporting Bugs
Include in your bug report:
- Python version
- Operating system
- Browser version
- Configuration (without credentials)
- Error messages and logs
- Steps to reproduce
