import os
import json
import requests
import time
from dotenv import load_dotenv
from pathlib import Path
from tqdm import tqdm
from datetime import datetime, timedelta
from collections import deque

class RateLimiter:
    def __init__(self, max_requests, time_window):
        self.max_requests = max_requests
        self.time_window = time_window  # in seconds
        self.requests = deque()

    def wait_if_needed(self):
        now = datetime.now()
        
        # Remove requests older than the time window
        while self.requests and (now - self.requests[0]) > timedelta(seconds=self.time_window):
            self.requests.popleft()
        
        # If we've hit the rate limit, wait until we can make another request
        if len(self.requests) >= self.max_requests:
            sleep_time = (self.requests[0] + timedelta(seconds=self.time_window) - now).total_seconds()
            if sleep_time > 0:
                time.sleep(sleep_time)
                # After sleeping, clear old requests
                self.requests.popleft()
        
        # Add the current request
        self.requests.append(now)

class PinterestExporter:
    VALID_IMAGE_SIZES = ['original', '600x', '236x']
    
    def __init__(self):
        load_dotenv()
        self.access_token = os.getenv('PINTEREST_ACCESS_TOKEN')
        self.api_base_url = 'https://api.pinterest.com/v5'
        self.headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Accept': 'application/json'
        }
        
        # Get output directory configuration
        output_dir = os.getenv('PINTEREST_OUTPUT_DIR', 'pinterest_export')
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.images_dir = self.output_dir / 'images'
        self.images_dir.mkdir(exist_ok=True)
        
        # Get image size configuration
        self.image_size = os.getenv('PINTEREST_IMAGE_SIZE', 'original')
        if self.image_size not in self.VALID_IMAGE_SIZES:
            print(f"Warning: Invalid image size '{self.image_size}', defaulting to 'original'")
            self.image_size = 'original'
        
        print(f"Output directory: {self.output_dir}")
        print(f"Image size: {self.image_size}")
        
        # Get rate limiting configuration from environment
        requests_per_hour = int(os.getenv('PINTEREST_REQUESTS_PER_HOUR', '1000'))
        safety_factor = float(os.getenv('RATE_LIMIT_SAFETY_FACTOR', '0.8'))
        
        # Calculate rate limits
        # Convert hourly limit to per-minute limit with safety factor
        safe_requests_per_hour = int(requests_per_hour * safety_factor)
        requests_per_minute = int(safe_requests_per_hour / 60)
        
        print(f"Rate limiting configured to {requests_per_minute} requests per minute")
        print(f"({safe_requests_per_hour} requests per hour, {safety_factor*100}% of maximum)")
        
        # Initialize rate limiter
        self.rate_limiter = RateLimiter(max_requests=requests_per_minute, time_window=60)

    def make_request(self, url, params=None, stream=False):
        """Make a rate-limited request to the Pinterest API."""
        self.rate_limiter.wait_if_needed()
        response = requests.get(url, headers=self.headers, params=params, stream=stream)
        
        # Handle rate limit errors
        if response.status_code == 429:  # Too Many Requests
            retry_after = int(response.headers.get('Retry-After', 60))
            print(f"\nRate limit reached. Waiting {retry_after} seconds...")
            time.sleep(retry_after)
            return self.make_request(url, params, stream)
            
        response.raise_for_status()
        return response

    def get_user_pins(self, bookmark=None):
        """Fetch all pins for the authenticated user."""
        endpoint = f'{self.api_base_url}/pins'
        params = {
            'page_size': 100,
            'bookmark': bookmark
        }
        
        response = self.make_request(endpoint, params=params)
        return response.json()

    def download_image(self, pin_media, pin_id):
        """Download an image and save it to the images directory."""
        # Get the appropriate image URL based on configured size
        if self.image_size in pin_media['images']:
            image_url = pin_media['images'][self.image_size]['url']
        else:
            print(f"Warning: Size {self.image_size} not available for pin {pin_id}, using original")
            image_url = pin_media['images']['original']['url']
        
        response = self.make_request(image_url, stream=True)
        
        file_extension = image_url.split('.')[-1].split('?')[0]
        if file_extension not in ['jpg', 'jpeg', 'png', 'gif']:
            file_extension = 'jpg'
            
        image_path = self.images_dir / f'{pin_id}.{file_extension}'
        with open(image_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        return image_path

    def export_all_pins(self):
        """Export all pins and their images."""
        all_pins = []
        bookmark = None
        
        print("Fetching pins...")
        while True:
            try:
                data = self.get_user_pins(bookmark)
                pins = data.get('items', [])
                if not pins:
                    break
                    
                all_pins.extend(pins)
                print(f"Fetched {len(all_pins)} pins so far...")
                
                bookmark = data.get('bookmark')
                if not bookmark:
                    break
                    
            except requests.exceptions.RequestException as e:
                print(f"Error fetching pins: {e}")
                break

        print(f"\nDownloading {len(all_pins)} images...")
        for pin in tqdm(all_pins):
            try:
                if 'media' in pin and 'images' in pin['media']:
                    image_path = self.download_image(pin['media'], pin['id'])
                    pin['local_image_path'] = str(image_path)
            except Exception as e:
                print(f"Error downloading image for pin {pin['id']}: {e}")

        # Save metadata to JSON file
        metadata_path = self.output_dir / 'pins_metadata.json'
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(all_pins, f, indent=2, ensure_ascii=False)
            
        print(f"\nExport complete!")
        print(f"Metadata saved to: {metadata_path}")
        print(f"Images saved to: {self.images_dir}")

def main():
    try:
        exporter = PinterestExporter()
        exporter.export_all_pins()
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    main() 