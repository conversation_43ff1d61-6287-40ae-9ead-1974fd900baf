# Pinterest Browser Exporter - Quick Reference

## Installation & Setup

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Configure authentication
cp .env.browser.example .env.browser
# Edit .env.browser with your Pinterest credentials

# 3. Run the exporter
python pinterest_browser_export.py
```

## Common Commands

### Basic Export
```bash
# Export all boards
python pinterest_browser_export.py

# Export specific boards (use board slugs)
python pinterest_browser_export.py --boards art-inspiration,home-decor

# Skip certain boards
python pinterest_browser_export.py --skip photography,recipes
```

### Maintenance
```bash
# Re-crawl a specific board
python pinterest_browser_export.py --recrawl board-slug

# Check for missing images
python pinterest_browser_export.py --check-missing

# Sync image data with JSON files
python pinterest_browser_export.py --sync-images
```

### Analysis & Reports
```bash
# Generate statistics
python pinterest_browser_export.py --stats

# Compare remote vs local counts
python pinterest_browser_export.py --compare-counts

# Generate HTML gallery reports
python pinterest_browser_export.py --html-reports

# Video pins report
python pinterest_browser_export.py --video-report
```

### Data Enhancement
```bash
# Populate missing titles
python pinterest_browser_export.py --rescan-titles

# Populate missing descriptions
python pinterest_browser_export.py --rescan-descriptions
```

## Configuration Quick Tips

### Essential Settings (.env.browser)
```env
# Authentication (required)
PINTEREST_EMAIL=<EMAIL>
PINTEREST_PASSWORD=your-password
PINTEREST_USERNAME=your-username

# Rate limiting (important!)
MAX_PINS_PER_MINUTE=10  # Start conservative
ACTION_DELAY_MIN=2.0
ACTION_DELAY_MAX=5.0

# Browser mode
BROWSER_HEADLESS=false  # Set true for background operation
```

### Performance Tuning
- **Slow/Getting Blocked**: Increase delays, reduce `MAX_PINS_PER_MINUTE`
- **Too Slow**: Decrease delays, increase `MAX_PINS_PER_MINUTE`
- **Browser Issues**: Set `BROWSER_HEADLESS=true`
- **Large Collections**: Use `original` image size, run overnight

## Board Slugs

Board slugs are URL-friendly versions of board names:
- "Art & Design" → `art-design`
- "Home Décor Ideas" → `home-decor-ideas`
- "Travel Photography" → `travel-photography`

Find them in your Pinterest board URLs: `pinterest.com/username/board-slug/`

## Output Structure

```
pinterest_export/
├── images/                    # Downloaded images by board
│   ├── art-inspiration/
│   └── home-decor/
├── collections/              # JSON metadata files
│   ├── art-inspiration.json
│   └── home-decor.json
├── reports/                  # HTML gallery reports
├── download_progress.json    # Resume tracking
└── pinterest_export.log     # Detailed logs
```

## Troubleshooting

### Common Issues
| Problem | Solution |
|---------|----------|
| Login failed | Check credentials in `.env.browser` |
| Browser crashes | Set `BROWSER_HEADLESS=true` |
| Getting blocked | Increase delays, reduce rate limit |
| Missing images | Run `--check-missing` then `--recrawl board-name` |
| Slow downloads | Reduce delays (but risk being blocked) |

### Getting Help
```bash
# Show all available options
python pinterest_browser_export.py --help

# Check logs for detailed error info
tail -f pinterest_export/pinterest_export.log
```

## Best Practices

1. **Start Conservative**: Use default rate limiting settings first
2. **Monitor Logs**: Watch for errors or blocking indicators
3. **Run Overnight**: Large collections take time
4. **Regular Backups**: Export periodically to catch new pins
5. **Respect Pinterest**: Don't overload their servers

## Legal Notice

This tool is for personal use only. Respect Pinterest's Terms of Service and copyright laws.
