# Pinterest API Authentication
PINTEREST_APP_ID=your_app_id
PINTEREST_APP_SECRET=your_app_secret
PINTEREST_ACCESS_TOKEN=your_access_token

# API Rate Limiting
PINTEREST_REQUESTS_PER_HOUR=1000  # Pinterest's default limit
RATE_LIMIT_SAFETY_FACTOR=0.8  # Percentage of max rate to use (0.8 = 80%)
MAX_RETRIES=3  # Maximum number of retries for failed requests
RETRY_DELAY=5  # Delay (seconds) between retries

# API Request Configuration
PAGE_SIZE=100  # Number of pins to fetch per request
REQUEST_TIMEOUT=30  # Timeout for API requests in seconds

# Image Download Configuration
PINTEREST_IMAGE_SIZE=original  # Options: 'original', '600x', '236x'
DOWNLOAD_TIMEOUT=30  # Timeout for image downloads in seconds
MAX_CONCURRENT_DOWNLOADS=5  # Maximum number of concurrent image downloads

# Output Configuration
PINTEREST_OUTPUT_DIR=pinterest_export
SAVE_FAILED_PINS=true  # Whether to save information about failed pin downloads
ORGANIZE_BY_BOARD=false  # Whether to organize pins in subdirectories by board name

# Logging Configuration
LOG_LEVEL=INFO  # Options: DEBUG, INFO, WARNING, ERROR
SAVE_API_RESPONSES=false  # Whether to save raw API responses for debugging 