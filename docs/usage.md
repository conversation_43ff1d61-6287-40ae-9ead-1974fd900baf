# Usage Guide

## Getting Started

### First Time Setup

1. **Install dependencies and configure**:
   ```bash
   pip install -r requirements.txt
   cp .env.browser.example .env.browser
   # Edit .env.browser with your Pinterest credentials
   ```

2. **Test the setup**:
   ```bash
   python pinterest_browser_export.py --stats
   ```

3. **Run your first export**:
   ```bash
   python pinterest_browser_export.py
   ```

## Basic Usage Patterns

### Full Export (Recommended for first run)
```bash
python pinterest_browser_export.py
```
This will:
- Log into Pinterest
- Discover all your boards
- Download all pins and images
- Save everything to `pinterest_export/`

### Selective Export
```bash
# Export only specific boards
python pinterest_browser_export.py --boards art,design,photography

# Export all except certain boards
python pinterest_browser_export.py --skip private-board,work-stuff
```

### Incremental Updates (Recommended for maintenance)
```bash
# Check what's new on Pinterest vs local
python pinterest_browser_export.py --compare-counts

# Update all boards with new remote pins
python pinterest_browser_export.py --update-boards all

# Update specific boards with new remote pins
python pinterest_browser_export.py --update-boards art,design

# Force update even if counts match
python pinterest_browser_export.py --update-boards all --force-update
```

## Understanding Board Slugs

Board slugs are the URL-friendly names Pinterest uses. You can find them in your board URLs:

```
https://pinterest.com/yourusername/art-and-design/
                                  ^^^^^^^^^^^^^^
                                  This is the board slug
```

Common transformations:
- Spaces become hyphens: "Art Design" → `art-design`
- Special characters are removed: "Art & Design" → `art-design`
- Everything is lowercase: "TRAVEL" → `travel`

## Common Workflows

### Initial Setup and Full Export
```bash
# 1. Setup
cp .env.browser.example .env.browser
# Edit .env.browser with your credentials

# 2. Test configuration
python pinterest_browser_export.py --stats

# 3. Full export (run overnight for large collections)
python pinterest_browser_export.py
```

### Regular Maintenance
```bash
# Weekly: Check for new pins and update them
python pinterest_browser_export.py --compare-counts
python pinterest_browser_export.py --update-boards all

# Monthly: Full re-scan of active boards (if needed)
python pinterest_browser_export.py --boards active-board1,active-board2

# As needed: Generate fresh HTML reports
python pinterest_browser_export.py --html-reports
```

### Update vs Recrawl

**Use `--recrawl`** (recommended for single boards):
- ✅ **Direct processing** - skips board discovery, goes straight to the board
- ✅ **Smart collection handling** - uses existing collection if available
- ✅ **Efficient** - only processes undownloaded pins from existing collection
- ✅ **Fast** - no unnecessary board listing or comparison
- ✅ **Safe** - preserves existing downloads and metadata
- ✅ **Creates new collection** if none exists

**Use `--update-boards`** (for multiple boards or when you need count comparison):
- ❌ **Requires board discovery** - fetches all boards first (slower)
- ✅ **Count comparison** - compares remote vs local pin counts
- ✅ **Batch processing** - can update multiple boards or all boards with discrepancies
- ✅ **Adds new pins** to existing collections
- ⚠️ **May be slower** due to board discovery and comparison process

### When to Use Which Command

**Use `--recrawl board-name` when:**
- ✅ You want to process a single specific board
- ✅ You want the fastest processing (no board discovery)
- ✅ You have an existing collection and want to download missing pins
- ✅ You want to create a new collection for a board
- ✅ You're troubleshooting a specific board

**Use `--update-boards all` when:**
- ✅ You want to update all boards that have new pins on Pinterest
- ✅ You want to see a comparison of remote vs local pin counts first
- ✅ You want to process multiple boards in one command
- ✅ You don't mind the slower board discovery process

**Use `--update-boards board1,board2` when:**
- ✅ You want to update multiple specific boards
- ✅ You want count comparison for those boards
- ⚠️ Note: `--recrawl` is usually faster for single boards

### Troubleshooting Workflow
```bash
# 1. Check what's missing
python pinterest_browser_export.py --check-missing

# 2. List pins for manual verification
python pinterest_browser_export.py --list-pins problematic-board

# 3. Process specific board (recommended - faster)
python pinterest_browser_export.py --recrawl problematic-board

# 4. Sync data files
python pinterest_browser_export.py --sync-images

# 5. Generate fresh reports
python pinterest_browser_export.py --html-reports
```

## Analysis and Reports

### Statistics and Analysis
```bash
# See statistics about your collection
python pinterest_browser_export.py --stats

# Compare what's on Pinterest vs what you have locally
python pinterest_browser_export.py --compare-counts

# Check for missing images
python pinterest_browser_export.py --check-missing

# Generate video pins report
python pinterest_browser_export.py --video-report
```

### HTML Reports
```bash
# Generate HTML galleries for all boards
python pinterest_browser_export.py --html-reports

# Generate HTML reports for specific boards
python pinterest_browser_export.py --html-reports studio,fun,electronics
```

## Maintenance Operations

### Metadata Enhancement
```bash
# Populate missing titles
python pinterest_browser_export.py --rescan-titles

# Populate missing descriptions
python pinterest_browser_export.py --rescan-descriptions

# Rescan specific boards for metadata
python pinterest_browser_export.py --boards studio --rescan-titles --rescan-descriptions
```

### Data Synchronization
```bash
# Sync image data with JSON files
python pinterest_browser_export.py --sync-images
```

## Monitoring Progress

### Real-time Logs
```bash
# Watch logs in real-time (Linux/Mac)
tail -f pinterest_export/pinterest_export.log

# On Windows
Get-Content pinterest_export/pinterest_export.log -Wait
```

### Progress Files
The script creates `pinterest_export/download_progress.json` to track:
- Which pins have been downloaded
- Which boards are complete
- Failed downloads for retry

## Resuming Interrupted Downloads

The script automatically resumes where it left off. If interrupted:
1. Just run the same command again
2. It will skip already downloaded content
3. Continue from where it stopped

## Performance Expectations

### Typical Speeds
- **Small boards** (< 100 pins): 5-15 minutes
- **Medium boards** (100-1000 pins): 30-90 minutes  
- **Large boards** (1000+ pins): 2-6 hours
- **Full account** (10+ boards): Several hours to days

### Factors Affecting Speed
- **Rate limiting settings**: Lower delays = faster but riskier
- **Internet connection**: Faster connection = faster downloads
- **Pinterest's response**: They may slow down requests
- **Image sizes**: Original quality takes longer
- **Browser mode**: Headless is slightly faster

## Configuration Tips

### Rate Limiting (Important!)
Start with conservative settings to avoid being blocked:
```env
MAX_PINS_PER_MINUTE=10
ACTION_DELAY_MIN=2.0
ACTION_DELAY_MAX=5.0
```

If you're not getting blocked, you can gradually:
- Increase `MAX_PINS_PER_MINUTE`
- Decrease delay values

### Browser Settings
```env
# For better performance (no visible browser)
BROWSER_HEADLESS=true

# For debugging (see what's happening)
BROWSER_HEADLESS=false
```

### Image Quality
```env
# Best quality (largest files)
PINTEREST_IMAGE_SIZE=original

# Good balance
PINTEREST_IMAGE_SIZE=600x

# Fastest download (smallest files)
PINTEREST_IMAGE_SIZE=236x
```

## Getting Support

1. **Check the logs**: `pinterest_export/pinterest_export.log`
2. **Review configuration**: Ensure `.env.browser` is correct
3. **Try conservative settings**: Increase delays, reduce rate limits
4. **Use built-in help**: `python pinterest_browser_export.py --help`
5. **Check documentation**: Review other docs in this folder

## Legal and Ethical Use

- **Personal use only**: Don't redistribute downloaded content
- **Respect rate limits**: Don't overload Pinterest's servers
- **Follow Terms of Service**: Pinterest's rules still apply
- **Copyright awareness**: Downloaded content may be copyrighted
