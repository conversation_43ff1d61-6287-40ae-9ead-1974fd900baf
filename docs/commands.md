# Command Reference

Complete reference for all command-line options available in the Pinterest Browser Exporter.

## Basic Syntax

```bash
python pinterest_browser_export.py [OPTIONS]
```

## Export Commands

### Basic Export
```bash
# Export all boards
python pinterest_browser_export.py

# Export specific boards (use board slugs)
python pinterest_browser_export.py --boards board1,board2,board3

# Skip specific boards during export
python pinterest_browser_export.py --skip unwanted-board,another-board
```

**Options:**
- `--boards BOARDS`: Comma-separated list of board slugs to export
- `--skip SKIP`: Comma-separated list of board slugs to skip

### Re-crawl Operations
```bash
# Re-crawl a specific board (processes existing collection or creates new one)
python pinterest_browser_export.py --recrawl board-slug
```

**Options:**
- `--recrawl BOARD`: Process a specific board directly without board discovery. If a collection exists, it processes undownloaded pins. If no collection exists, it creates a new one.

**Behavior:**
- ✅ **Skips board listing** - goes directly to the specified board
- ✅ **Uses existing collection** if available, only processes undownloaded pins
- ✅ **Creates new collection** if none exists
- ✅ **Efficient** - doesn't re-download existing pins unless they failed

### Incremental Updates
```bash
# Update all boards with new remote pins
python pinterest_browser_export.py --update-boards all

# Update specific boards with new remote pins
python pinterest_browser_export.py --update-boards board1,board2

# Force update even if pin counts match
python pinterest_browser_export.py --update-boards all --force-update
```

**Options:**
- `--update-boards BOARDS`: Update boards with new pins (`all` or comma-separated list)
- `--force-update`: Force update even if remote and local pin counts match

**Behavior:**
- ❌ **Requires board discovery** - fetches all boards from Pinterest first (slower)
- ✅ **Compares pin counts** - only updates boards with more remote pins than local
- ✅ **Adds new pins** to existing collections
- ✅ **Preserves existing data** - doesn't re-download existing pins
- ⚠️ **May scroll through boards twice** due to comparison process

## Analysis Commands

### Statistics and Comparison
```bash
# Generate download statistics
python pinterest_browser_export.py --stats

# Compare remote vs local pin counts (terse tabular output)
python pinterest_browser_export.py --compare-counts

# Check for missing image files
python pinterest_browser_export.py --check-missing

# Generate video pins report
python pinterest_browser_export.py --video-report
```

**Options:**
- `--stats`: Show download statistics and collection summary
- `--compare-counts`: Compare Pinterest pin counts with local downloads
- `--check-missing`: Identify pins with missing image files
- `--video-report`: Generate report of all video pins

## Report Generation

### HTML Reports
```bash
# Generate HTML gallery reports for all boards
python pinterest_browser_export.py --html-reports

# Generate HTML reports for specific boards
python pinterest_browser_export.py --html-reports board1,board2,board3
```

**Options:**
- `--html-reports [BOARDS]`: Generate HTML gallery reports (optional comma-separated board list)

## Maintenance Commands

### Metadata Enhancement
```bash
# Rescan pins to populate missing titles
python pinterest_browser_export.py --rescan-titles

# Rescan pins to populate missing descriptions
python pinterest_browser_export.py --rescan-descriptions

# Rescan specific boards for metadata
python pinterest_browser_export.py --boards board1,board2 --rescan-titles --rescan-descriptions
```

**Options:**
- `--rescan-titles`: Scan pin pages to populate missing titles
- `--rescan-descriptions`: Scan pin pages to populate missing descriptions

### Data Synchronization
```bash
# Sync image data with JSON collection files
python pinterest_browser_export.py --sync-images
```

**Options:**
- `--sync-images`: Update JSON files to reflect actually downloaded images

### Pin Listing and Verification
```bash
# List all pin URLs for a specific board (for manual verification)
python pinterest_browser_export.py --list-pins studio-ghibli

# List pins for a board with spaces in the name
python pinterest_browser_export.py --list-pins "studio ghibli"
```

**Options:**
- `--list-pins BOARD`: List all pin URLs for a specific board slug with status and titles

## Command Combinations

### Common Combinations
```bash
# Export specific boards and generate reports
python pinterest_browser_export.py --boards art,design --html-reports

# Update boards and check for missing files
python pinterest_browser_export.py --update-boards all --check-missing

# Rescan metadata for specific boards
python pinterest_browser_export.py --boards studio,fun --rescan-titles --rescan-descriptions

# Full maintenance workflow
python pinterest_browser_export.py --update-boards all --sync-images --html-reports
```

## Help and Information

```bash
# Show help message with all options
python pinterest_browser_export.py --help
python pinterest_browser_export.py -h

# Show version information (if implemented)
python pinterest_browser_export.py --version
```

## Command Behavior Notes

### Board Selection Priority
1. If `--boards` is specified, only those boards are processed
2. If `--skip` is specified, those boards are excluded from processing
3. If both are specified, `--boards` takes precedence and `--skip` is ignored
4. If neither is specified, all boards are processed

### Update vs Recrawl
- **`--update-boards`**: Requires board discovery, compares counts, adds new pins to existing collections
- **`--recrawl`**: Direct board processing, uses existing collection if available, processes undownloaded pins

### Report Generation
- HTML reports are generated in `pinterest_export/reports/`
- Reports include clickable image galleries with metadata
- Reports can be generated for all boards or specific boards

### Metadata Rescanning
- `--rescan-titles` and `--rescan-descriptions` can be used together
- These operations visit pin pages to extract missing metadata
- They do not re-download images, only update text data

## Exit Codes

- `0`: Success
- `1`: General error (configuration, network, etc.)
- `2`: Authentication error
- `3`: Interrupted by user (Ctrl+C)

## Examples by Use Case

### First-Time User
```bash
# 1. Test setup
python pinterest_browser_export.py --stats

# 2. Export everything
python pinterest_browser_export.py

# 3. Generate reports
python pinterest_browser_export.py --html-reports
```

### Regular Maintenance
```bash
# 1. Check for new pins
python pinterest_browser_export.py --compare-counts

# 2. Update with new pins
python pinterest_browser_export.py --update-boards all

# 3. Generate fresh reports
python pinterest_browser_export.py --html-reports
```

### Troubleshooting
```bash
# 1. Check what's missing
python pinterest_browser_export.py --check-missing

# 2. Process problematic board (fastest method)
python pinterest_browser_export.py --recrawl problematic-board

# 3. Sync data files
python pinterest_browser_export.py --sync-images
```

### Selective Operations
```bash
# Work with specific boards only
python pinterest_browser_export.py --boards art,design,photography

# Skip certain boards
python pinterest_browser_export.py --skip private-stuff,work-boards

# Update only active boards
python pinterest_browser_export.py --update-boards active-board1,active-board2
```

## Performance Considerations

### Fast Operations (No Login Required)
- `--stats`
- `--check-missing`
- `--sync-images`
- `--html-reports` (if images already downloaded)
- `--list-pins` (reads local collection files)

### Pinterest Access Operations (Require Login)

**Fastest (Direct Board Processing):**
- `--recrawl board-name` - Goes directly to board, no discovery needed

**Medium Speed (Board Discovery Required):**
- `--update-boards board1,board2` - Discovers all boards, then processes specified ones
- `--compare-counts` - Discovers all boards for comparison

**Slowest (Full Processing):**
- Default export (all boards) - Discovers and processes all boards
- `--update-boards all` - Discovers all boards, compares counts, then processes discrepancies
- `--rescan-titles` / `--rescan-descriptions` - Visits individual pin pages

### Memory Usage
- Large board exports use more memory
- HTML report generation can be memory-intensive for large collections
- Use `--boards` to limit scope if memory is constrained

## Error Handling

The script includes comprehensive error handling:
- Network timeouts are retried automatically
- Authentication failures are reported clearly
- Missing files are logged but don't stop execution
- Progress is saved regularly to enable resuming

For detailed error information, check the log file at `pinterest_export/pinterest_export.log`.
