# Installation Guide

## Prerequisites

- Python 3.7 or higher
- Google Chrome or Chromium browser
- Stable internet connection

## Step-by-Step Installation

### 1. Clone or Download the Repository

```bash
git clone <repository-url>
cd pinterest-export
```

### 2. Create Virtual Environment (Recommended)

```bash
# Create virtual environment
python -m venv .venv

# Activate virtual environment
# On macOS/Linux:
source .venv/bin/activate

# On Windows:
.venv\Scripts\activate
```

### 3. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 4. Verify Chrome Installation

The script will automatically download ChromeDriver, but ensure Chrome is installed:

- **Windows**: Download from Google Chrome website
- **macOS**: `brew install --cask google-chrome`
- **Linux**: `sudo apt-get install google-chrome-stable`

### 5. Create Configuration File

```bash
cp .env.browser.example .env.browser
```

### 6. Configure Authentication

Edit `.env.browser` with your Pinterest credentials:

```env
PINTEREST_EMAIL=<EMAIL>
PINTEREST_PASSWORD=your-password
PINTEREST_USERNAME=your-username
```

### 7. Test the Setup

```bash
# Test configuration (doesn't require login)
python pinterest_browser_export.py --stats

# Test login and basic functionality
python pinterest_browser_export.py --compare-counts
```

## Troubleshooting Installation

### Python Version Issues

Check your Python version:
```bash
python --version
```

If you have multiple Python versions, you may need to use `python3`:
```bash
python3 --version
python3 -m pip install -r requirements.txt
```

### Virtual Environment Issues

If virtual environment activation fails:
```bash
# On Windows, try:
.venv\Scripts\activate.bat

# Or use PowerShell:
.venv\Scripts\Activate.ps1
```

### Chrome/ChromeDriver Issues

If you get ChromeDriver errors:
1. Ensure Chrome is installed and up to date
2. The script will automatically download the correct ChromeDriver version
3. If issues persist, try running in headless mode: `BROWSER_HEADLESS=true`

### Permission Issues

On macOS/Linux, you may need to make the script executable:
```bash
chmod +x pinterest_browser_export.py
```

### Dependency Installation Issues

If pip install fails:
```bash
# Update pip first
pip install --upgrade pip

# Install dependencies one by one to identify issues
pip install selenium
pip install requests
pip install python-dotenv
# ... etc
```

## System-Specific Notes

### macOS

- You may need to allow Chrome to run in System Preferences > Security & Privacy
- If using Homebrew Python, ensure it's in your PATH

### Windows

- Use Command Prompt or PowerShell as Administrator if you encounter permission issues
- Ensure Python is added to your system PATH during installation

### Linux

- Install Chrome using your distribution's package manager
- You may need to install additional dependencies:
  ```bash
  sudo apt-get update
  sudo apt-get install -y wget unzip xvfb
  ```

## Verification

After installation, verify everything works:

```bash
# 1. Check Python and dependencies
python -c "import selenium, requests, dotenv; print('All dependencies installed')"

# 2. Check configuration
python pinterest_browser_export.py --help

# 3. Test basic functionality (no login required)
python pinterest_browser_export.py --stats

# 4. Test login (requires valid credentials)
python pinterest_browser_export.py --compare-counts
```

If all commands run without errors, your installation is complete!

## Next Steps

- Review the [Configuration Guide](configuration.md) for detailed settings
- Check the [Usage Guide](usage.md) for common workflows
- See the [Command Reference](commands.md) for all available options
