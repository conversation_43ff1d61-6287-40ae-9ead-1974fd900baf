# Configuration Guide

The Pinterest Browser Exporter uses a `.env.browser` file for configuration. This file contains all the settings needed to customize the tool's behavior.

## Environment File Setup

Create your configuration file from the example:
```bash
cp .env.browser.example .env.browser
```

## Configuration Categories

### Authentication Settings (Required)

```env
PINTEREST_EMAIL=<EMAIL>
PINTEREST_PASSWORD=your-password
PINTEREST_USERNAME=your-username
```

- **PINTEREST_EMAIL**: Your Pinterest login email address
- **PINTEREST_PASSWORD**: Your Pinterest account password
- **PINTEREST_USERNAME**: Your Pinterest username (used in URLs)

**Important**: Keep these credentials secure and never commit them to version control.

### Browser Configuration

```env
BROWSER_HEADLESS=false              # true = run in background, false = visible browser
BROWSER_VIEWPORT_WIDTH=1280         # Browser window width in pixels
BROWSER_VIEWPORT_HEIGHT=800         # Browser window height in pixels
```

- **BROWSER_HEADLESS**: 
  - `false` (default): Shows browser window (useful for debugging)
  - `true`: Runs browser in background (better performance, less resource usage)
- **BROWSER_VIEWPORT_WIDTH/HEIGHT**: Browser window dimensions
  - Larger sizes may help with page rendering
  - Smaller sizes use less memory

### Rate Limiting (Important for avoiding blocks)

```env
ACTION_DELAY_MIN=2.0               # Minimum delay between actions (seconds)
ACTION_DELAY_MAX=5.0               # Maximum delay between actions (seconds)
SCROLL_DELAY_MIN=1.0               # Minimum delay between scrolls (seconds)
SCROLL_DELAY_MAX=3.0               # Maximum delay between scrolls (seconds)
DOWNLOAD_DELAY_MIN=3.0             # Minimum delay between downloads (seconds)
DOWNLOAD_DELAY_MAX=7.0             # Maximum delay between downloads (seconds)
MAX_PINS_PER_MINUTE=10             # Maximum pins to process per minute (0 = no limit)
```

**Rate Limiting Strategy**:
- Start with conservative settings (higher delays, lower rate limits)
- If not getting blocked, gradually decrease delays and increase rate limits
- If getting blocked, increase delays and reduce rate limits

### Output Settings

```env
PINTEREST_OUTPUT_DIR=pinterest_export    # Directory to save all output files
PINTEREST_IMAGE_SIZE=original            # Image quality: original, 600x, or 236x
SAVE_FAILED_PINS=true                   # Save information about failed downloads
```

- **PINTEREST_OUTPUT_DIR**: Base directory for all exported content
- **PINTEREST_IMAGE_SIZE**: 
  - `original`: Highest quality, largest files, slowest downloads
  - `600x`: Good balance of quality and speed
  - `236x`: Fastest downloads, smallest files, lower quality
- **SAVE_FAILED_PINS**: Whether to save metadata for pins that failed to download

## Configuration Examples

### Conservative Settings (Recommended for first run)
```env
# Safe settings to avoid being blocked
MAX_PINS_PER_MINUTE=5
ACTION_DELAY_MIN=3.0
ACTION_DELAY_MAX=7.0
DOWNLOAD_DELAY_MIN=5.0
DOWNLOAD_DELAY_MAX=10.0
BROWSER_HEADLESS=true
PINTEREST_IMAGE_SIZE=600x
```

### Aggressive Settings (Use with caution)
```env
# Faster but higher risk of being blocked
MAX_PINS_PER_MINUTE=20
ACTION_DELAY_MIN=1.0
ACTION_DELAY_MAX=2.0
DOWNLOAD_DELAY_MIN=1.0
DOWNLOAD_DELAY_MAX=3.0
BROWSER_HEADLESS=true
PINTEREST_IMAGE_SIZE=original
```

### Debug Settings
```env
# Best for troubleshooting
BROWSER_HEADLESS=false
BROWSER_VIEWPORT_WIDTH=1920
BROWSER_VIEWPORT_HEIGHT=1080
MAX_PINS_PER_MINUTE=5
ACTION_DELAY_MIN=5.0
ACTION_DELAY_MAX=10.0
```

## Advanced Configuration

### Multiple Accounts

To use multiple Pinterest accounts, create separate configuration files:

```bash
# Create separate config files
cp .env.browser .env.browser.account1
cp .env.browser .env.browser.account2

# Edit each with different credentials
# Then run with specific config:
DOTENV_PATH=.env.browser.account1 python pinterest_browser_export.py
DOTENV_PATH=.env.browser.account2 python pinterest_browser_export.py
```

### Performance Tuning

#### For Large Collections
- Use `BROWSER_HEADLESS=true` for better performance
- Increase `MAX_PINS_PER_MINUTE` if not getting blocked
- Use SSD storage for better I/O performance
- Run during off-peak hours

#### For Slow Connections
- Increase timeout values (modify in code if needed)
- Reduce concurrent operations
- Use smaller image sizes (`PINTEREST_IMAGE_SIZE=600x`)
- Increase delays to account for slower responses

#### For Limited Storage
- Use `PINTEREST_IMAGE_SIZE=236x` for smaller files
- Regularly clean up old exports
- Consider using external storage

### Environment Variables

You can also set configuration via environment variables:

```bash
# Override specific settings
export PINTEREST_EMAIL="<EMAIL>"
export BROWSER_HEADLESS=true
export MAX_PINS_PER_MINUTE=15

# Run the script
python pinterest_browser_export.py
```

## Configuration Validation

The script validates your configuration on startup and will show:
- Which settings are loaded
- Any missing required settings
- Warnings about potentially problematic settings

Example output:
```
Configuration loaded:
  - Email: ***
  - Password: ***
  - Username: johndoe
  - Output directory: pinterest_export
  - Rate limit: 10 pins/minute
```

## Security Considerations

### Credential Security
- Never commit `.env.browser` to version control
- Use strong, unique passwords
- Consider using application-specific passwords if available
- Regularly rotate credentials

### Network Security
- Use secure networks when running the tool
- Consider using a VPN if accessing from public networks
- Be aware that the tool makes many requests to Pinterest

## Troubleshooting Configuration

### Common Issues

1. **Configuration file not found**: Ensure `.env.browser` exists in the project root
2. **Invalid credentials**: Double-check email, password, and username
3. **Rate limiting too aggressive**: Pinterest may block if settings are too fast
4. **Browser issues**: Try headless mode or adjust viewport size

### Testing Configuration

```bash
# Test configuration loading (no login required)
python pinterest_browser_export.py --stats

# Test login with current settings
python pinterest_browser_export.py --compare-counts
```

### Configuration Debugging

Enable debug logging to see detailed configuration information:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Best Practices

1. **Start Conservative**: Use default settings first, then optimize
2. **Monitor Performance**: Watch logs for blocking or errors
3. **Regular Backups**: Keep backups of working configurations
4. **Document Changes**: Note what settings work for your use case
5. **Respect Limits**: Don't push rate limits too aggressively
