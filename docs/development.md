# Development Notes

## Current Status

The Pinterest Browser Exporter is a mature tool with comprehensive functionality for downloading Pinterest content. Recent improvements include enhanced media viewer integration for maximum image resolution and improved incremental update capabilities.

## TODO List

### High Priority
- [ ] **Video Download Support**: Implement m3u8 parsing and MP4 extraction for video pins
- [ ] **Enhanced Rescan**: Make rescan functionality also open media viewer for better image quality
- [ ] **Progress Visualization**: Add spinner and progress bars for better user experience
- [ ] **Verbose Mode**: Implement detailed verbose logging option
- [ ] **Missing Image Recovery**: Convert `--check-missing` to `--get-missing` with progress tracking

### Medium Priority
- [ ] **Google Image Search Integration**: Find higher resolution versions of low-res images
- [ ] **Image Upscaling**: Implement AI upscaling for images with no better versions available
- [ ] **Deduplication**: Implement blur-based image comparison for duplicate detection
- [ ] **Startup Board Comparison**: Compare remote boards with local on startup and report differences
- [ ] **Clean Exit Handling**: Suppress connection errors and properly release Chrome processes

### Low Priority
- [ ] **Image Analysis**: OCR, object detection, and content analysis
- [ ] **Tagging System**: Automatic tagging based on image analysis
- [ ] **Color Palette Analysis**: Group and search by color palette
- [ ] **Moodboard Generation**: Create PDF moodboards using keywords
- [ ] **Knowledge Tool Integration**: Export/import to external knowledge management tools

### Completed Features ✅
- [x] **Metadata Rescanning**: Mode to revisit pins and add missing metadata
- [x] **Resume Capability**: Normal mode resumes without redownloading unless forced
- [x] **HTML Reports**: Command line option for HTML gallery generation
- [x] **Clickable Reports**: HTML reports with image embiggen functionality
- [x] **Session Cleanup**: Proper browser session cleanup
- [x] **Final Collection Pass**: Only if not all expected pins found
- [x] **Clean HTML Format**: Improved gallery report formatting
- [x] **Incremental Updates**: New pins scroll detection and stopping at existing pins
- [x] **Media Viewer Integration**: Enhanced image resolution detection and download

## Development Ideas

### Image Corpus Utilization
- **Moodboard Generation**: Use keywords and PDF library or Pillow to create themed collections
- **Color-based Search**: Group and search images by dominant color palettes
- **Similarity Analysis**: Find visually similar images across boards
- **Content Analysis**: Automatic categorization based on image content

### Advanced Features
- **Smart Organization**: Reorganize pins by subject matter or visual similarities
- **Trend Analysis**: Track pin popularity and engagement over time
- **Export Formats**: Support for various export formats (CSV, XML, etc.)
- **Backup Integration**: Automatic cloud backup of collections

## Technical Improvements

### Code Quality
- **Refactor Complex Functions**: Break down functions with high cognitive complexity
- **Type Hints**: Add comprehensive type annotations
- **Error Handling**: Improve specific exception handling
- **Constants**: Define constants for repeated string literals
- **Documentation**: Expand inline documentation and docstrings

### Performance Optimizations
- **Async Operations**: Implement async/await for I/O operations
- **Connection Pooling**: Optimize HTTP connection reuse
- **Memory Management**: Improve memory usage for large collections
- **Caching**: Implement intelligent caching strategies

### Architecture Improvements
- **Plugin System**: Modular architecture for extensibility
- **Configuration Management**: Enhanced configuration validation and management
- **Database Backend**: Optional database storage for large collections
- **API Interface**: REST API for programmatic access

## Recent Changes Log

### Media Viewer Enhancement (Latest)
- **Issue**: Pin download was not getting the largest possible image size
- **Solution**: Enhanced media viewer integration to:
  - Use correct selector `[data-test-id="media-viewer-image"] img`
  - Implement resolution upgrade logic (736x → originals)
  - Add URL validation for higher resolution versions
  - Provide detailed logging of resolution detection

### Incremental Sync Feature
- **Feature**: Add new remote pins to existing local collections without deleting existing data
- **Implementation**: `--update-boards` command with smart pin filtering
- **Benefits**: Faster updates, preserves existing downloads, safer operation

### HTML Reports Enhancement
- **Feature**: Accept comma-separated board slugs for generating reports for multiple specific boards
- **Usage**: `--html-reports studio,fun,electronics`
- **Benefits**: Selective report generation, better workflow integration

### Force Download/Recrawl Capability
- **Feature**: Force download/recrawl when local pins exist but media viewer access is needed
- **Implementation**: `--force-update` flag with `--update-boards`
- **Use Case**: When existing images are lower resolution than available

## Development Environment

### Setup
```bash
# Clone repository
git clone <repository-url>
cd pinterest-export

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # macOS/Linux
.venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Install development dependencies (if available)
pip install -r requirements-dev.txt
```

### Testing
```bash
# Run basic functionality tests
python pinterest_browser_export.py --stats
python pinterest_browser_export.py --help

# Test with single board
python pinterest_browser_export.py --boards test-board

# Test incremental updates
python pinterest_browser_export.py --update-boards test-board
```

### Code Style
- Follow PEP 8 guidelines
- Use meaningful variable and function names
- Add docstrings for all public functions
- Keep functions focused and single-purpose
- Use type hints where appropriate

## Contributing Guidelines

### Pull Request Process
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes with appropriate tests
4. Update documentation as needed
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Code Review Checklist
- [ ] Code follows style guidelines
- [ ] Changes are well-documented
- [ ] Tests pass (if applicable)
- [ ] No breaking changes to existing functionality
- [ ] Performance impact considered
- [ ] Security implications reviewed

### Bug Report Template
```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. See error

**Expected behavior**
What you expected to happen.

**Environment:**
- OS: [e.g. macOS 12.0]
- Python version: [e.g. 3.9.0]
- Browser version: [e.g. Chrome 96.0]

**Additional context**
Add any other context about the problem here.
```

## Research Notes

### Image Analysis Libraries
For future image analysis features, consider these Python libraries:

1. **Scikit-Image**: Versatile image processing, filtering, segmentation, feature extraction
2. **ImageAI**: Object detection and image recognition with pre-trained models
3. **OpenCV**: Computer vision, object detection, color analysis, segmentation
4. **YOLO**: Real-time object detection framework
5. **DETR**: Transformer-based object detection by Facebook AI

### Google Images Integration
For finding higher resolution versions:
- Use Google Images reverse search API
- Parse results for exact matches with higher resolution
- Implement download and replacement workflow
- Add user confirmation for replacements

### Video Processing
For video pin support:
- Parse m3u8 playlists for video segments
- Download and concatenate video segments
- Convert to standard formats (MP4)
- Extract thumbnails and metadata

## Architecture Decisions

### Browser Automation Choice
- **Chosen**: Selenium WebDriver with Chrome
- **Alternatives**: Playwright, Puppeteer
- **Reasoning**: Mature ecosystem, extensive documentation, Pinterest compatibility

### Data Storage Format
- **Chosen**: JSON files with organized directory structure
- **Alternatives**: SQLite database, CSV files
- **Reasoning**: Human-readable, easy to backup, no database dependencies

### Rate Limiting Strategy
- **Chosen**: Configurable random delays with per-minute limits
- **Alternatives**: Token bucket, fixed delays
- **Reasoning**: Mimics human behavior, configurable, effective

### Image Resolution Strategy
- **Chosen**: Media viewer integration with URL pattern matching
- **Alternatives**: API-based resolution detection, fixed resolution selection
- **Reasoning**: Accesses highest quality available, works with Pinterest's current system

## Future Architecture Considerations

### Scalability
- Consider database backend for very large collections (10,000+ boards)
- Implement distributed processing for multiple accounts
- Add cloud storage integration for backup and sync

### Maintainability
- Modularize selectors for easy Pinterest UI updates
- Implement plugin architecture for custom features
- Add comprehensive test suite for regression prevention

### User Experience
- Consider GUI interface for non-technical users
- Implement real-time progress visualization
- Add configuration wizard for first-time setup
