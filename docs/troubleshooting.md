# Troubleshooting Guide

## Common Issues and Solutions

### Installation Issues

#### Python Version Problems
**Symptom**: <PERSON><PERSON><PERSON> fails to run or import errors
**Solutions**:
```bash
# Check Python version (need 3.7+)
python --version

# If using multiple Python versions
python3 --version
python3 -m pip install -r requirements.txt
```

#### Virtual Environment Issues
**Symptom**: "Module not found" errors
**Solutions**:
```bash
# Ensure virtual environment is activated
source .venv/bin/activate  # macOS/Linux
.venv\Scripts\activate     # Windows

# Reinstall dependencies in virtual environment
pip install -r requirements.txt
```

#### Chrome/ChromeDriver Issues
**Symptom**: <PERSON><PERSON><PERSON> fails to start or ChromeDriver errors
**Solutions**:
1. Ensure Chrome is installed and up to date
2. Try headless mode: `BROWSER_HEADLESS=true` in `.env.browser`
3. Check Chrome version compatibility
4. Clear Chrome profile: delete `pinterest_export/chrome_profile/`

### Authentication Issues

#### Login Failed
**Symptom**: "Login failed" or authentication errors
**Solutions**:
1. Verify credentials in `.env.browser`
2. Check for two-factor authentication (not supported)
3. Try logging in manually in a regular browser first
4. Ensure account is not locked or suspended
5. Check for special characters in password (escape if needed)

#### Session Expired
**Symptom**: Works initially then fails with authentication errors
**Solutions**:
1. Restart the script (it will re-login automatically)
2. Clear browser profile: delete `pinterest_export/chrome_profile/`
3. Check if Pinterest requires re-authentication

### Rate Limiting and Blocking

#### Getting Blocked
**Symptom**: "Too many requests", "Rate limited", or sudden failures
**Solutions**:
1. Increase delays in `.env.browser`:
   ```env
   ACTION_DELAY_MIN=5.0
   ACTION_DELAY_MAX=10.0
   DOWNLOAD_DELAY_MIN=10.0
   DOWNLOAD_DELAY_MAX=15.0
   MAX_PINS_PER_MINUTE=5
   ```
2. Wait before retrying (several hours)
3. Use different IP address if possible
4. Run during off-peak hours

#### Slow Performance
**Symptom**: Script runs very slowly
**Solutions**:
1. Reduce delays (but risk being blocked):
   ```env
   ACTION_DELAY_MIN=1.0
   ACTION_DELAY_MAX=2.0
   MAX_PINS_PER_MINUTE=20
   ```
2. Use headless mode: `BROWSER_HEADLESS=true`
3. Use smaller image size: `PINTEREST_IMAGE_SIZE=600x`

### Browser Issues

#### Browser Crashes
**Symptom**: Browser closes unexpectedly or becomes unresponsive
**Solutions**:
1. Set `BROWSER_HEADLESS=true` in `.env.browser`
2. Reduce viewport size:
   ```env
   BROWSER_VIEWPORT_WIDTH=1024
   BROWSER_VIEWPORT_HEIGHT=768
   ```
3. Close other browser instances
4. Restart your computer
5. Clear browser profile: delete `pinterest_export/chrome_profile/`

#### Memory Issues
**Symptom**: High memory usage or out-of-memory errors
**Solutions**:
1. Use headless mode
2. Process boards individually: `--boards single-board`
3. Restart script periodically for large collections
4. Increase system memory or use swap space

### Data Issues

#### Missing Images
**Symptom**: JSON files exist but images are missing
**Solutions**:
```bash
# 1. Check what's missing
python pinterest_browser_export.py --check-missing

# 2. Re-download specific board
python pinterest_browser_export.py --recrawl board-name

# 3. Sync data files
python pinterest_browser_export.py --sync-images
```

#### Corrupted Collections
**Symptom**: Invalid JSON or incomplete data
**Solutions**:
1. Delete corrupted collection file: `rm pinterest_export/collections/board-name.json`
2. Re-crawl the board: `python pinterest_browser_export.py --recrawl board-name`
3. Check disk space and file permissions

#### Duplicate Pins
**Symptom**: Same pins downloaded multiple times
**Solutions**:
1. Use `--update-boards` instead of full re-export
2. Check for duplicate board names or slugs
3. Clear progress file if corrupted: `rm pinterest_export/download_progress.json`

### Network Issues

#### Connection Timeouts
**Symptom**: Frequent timeout errors
**Solutions**:
1. Check internet connection stability
2. Increase timeout values (modify in code if needed)
3. Use wired connection instead of WiFi
4. Try different DNS servers

#### SSL/Certificate Errors
**Symptom**: SSL certificate verification errors
**Solutions**:
1. Update certificates: `pip install --upgrade certifi`
2. Check system date/time
3. Try different network connection

### Configuration Issues

#### Configuration File Not Found
**Symptom**: "Configuration file not found" error
**Solutions**:
```bash
# Create configuration file
cp .env.browser.example .env.browser
# Edit with your credentials
```

#### Invalid Configuration
**Symptom**: Configuration validation errors
**Solutions**:
1. Check file format (no spaces around `=`)
2. Ensure all required fields are present
3. Check for special characters in values
4. Validate boolean values (`true`/`false`, not `True`/`False`)

## Debugging Techniques

### Enable Debug Logging
Add to the beginning of the script or create a debug version:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Check Log Files
```bash
# View recent log entries
tail -20 pinterest_export/pinterest_export.log

# Watch logs in real-time
tail -f pinterest_export/pinterest_export.log

# Search for specific errors
grep -i error pinterest_export/pinterest_export.log
```

### Test Individual Components
```bash
# Test configuration only
python pinterest_browser_export.py --stats

# Test login only
python pinterest_browser_export.py --compare-counts

# Test single board
python pinterest_browser_export.py --boards single-board-name
```

### Browser Debugging
1. Set `BROWSER_HEADLESS=false` to see what's happening
2. Add manual pauses in the code for inspection
3. Use browser developer tools if needed

## Performance Optimization

### For Large Collections
- Use `BROWSER_HEADLESS=true`
- Increase `MAX_PINS_PER_MINUTE` gradually
- Run during off-peak hours
- Use SSD storage
- Process boards individually if memory is limited

### For Slow Connections
- Increase timeout values
- Use smaller image sizes
- Reduce concurrent operations
- Use `--update-boards` instead of full re-export

## Getting Help

### Information to Include in Bug Reports
1. Python version: `python --version`
2. Operating system and version
3. Browser version
4. Configuration (without credentials)
5. Complete error messages and stack traces
6. Steps to reproduce the issue
7. Log file excerpts

### Self-Help Checklist
- [ ] Check this troubleshooting guide
- [ ] Review configuration settings
- [ ] Check log files for detailed errors
- [ ] Try with conservative rate limiting settings
- [ ] Test with a single board first
- [ ] Ensure all dependencies are installed
- [ ] Verify Pinterest account is accessible

### Debug Commands
```bash
# Test basic functionality
python pinterest_browser_export.py --help
python pinterest_browser_export.py --stats

# Test configuration and login
python pinterest_browser_export.py --compare-counts

# Test with minimal scope
python pinterest_browser_export.py --boards single-board --html-reports
```

## Known Limitations

1. **Two-Factor Authentication**: Not supported
2. **Private Boards**: May not be accessible depending on settings
3. **Video Pins**: Cannot download video content, only metadata
4. **Large Images**: Some very large images may timeout
5. **Rate Limits**: Pinterest may change rate limiting policies
6. **Browser Dependencies**: Requires Chrome/Chromium installation

## Recovery Procedures

### After System Crash
1. Check file system integrity
2. Verify configuration file exists
3. Check for corrupted JSON files
4. Resume with same command (script will continue where it left off)

### After Network Issues
1. Check internet connectivity
2. Clear browser profile if needed
3. Restart with conservative settings
4. Use `--update-boards` to catch up on missed pins

### After Pinterest Changes
1. Check if Pinterest has changed their interface
2. Update selectors in code if needed
3. Adjust rate limiting if policies changed
4. Check for new authentication requirements
