# Pinterest Browser Exporter

A comprehensive Pinterest board and pin exporter that uses browser automation to download all your Pinterest content, including high-resolution images and metadata. This tool bypasses API limitations by directly interacting with Pinterest's web interface.

## Features

- **Complete Board Export**: Downloads all pins from your Pinterest boards
- **High-Quality Images**: Automatically finds and downloads the largest available image resolution
- **Metadata Preservation**: Captures pin titles, descriptions, URLs, and board information
- **Resume Capability**: Automatically resumes interrupted downloads
- **Incremental Updates**: Add new remote pins to existing collections without deleting existing data
- **Smart Rate Limiting**: Configurable delays to avoid being blocked
- **Progress Tracking**: Detailed logging and progress reports
- **HTML Reports**: Generate beautiful gallery reports of your collections
- **Multiple Export Modes**: Export all boards, specific boards, or generate reports

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Authentication
Copy the example configuration and add your Pinterest credentials:
```bash
cp .env.browser.example .env.browser
```

Edit `.env.browser` with your Pinterest login details:
```env
PINTEREST_EMAIL=<EMAIL>
PINTEREST_PASSWORD=your-password
PINTEREST_USERNAME=your-username
```

### 3. Run the Exporter
```bash
# Export all boards
python pinterest_browser_export.py

# Export specific boards
python pinterest_browser_export.py --boards art,design,photography

# Generate statistics (no login required)
python pinterest_browser_export.py --stats
```

## Common Commands

### Basic Export Operations
```bash
# Export all boards
python pinterest_browser_export.py

# Export specific boards (use board slugs)
python pinterest_browser_export.py --boards art-inspiration,home-decor

# Skip certain boards during export
python pinterest_browser_export.py --skip photography,recipes

# Re-crawl a specific board (processes existing collection efficiently)
python pinterest_browser_export.py --recrawl art-inspiration
```

### Incremental Updates
```bash
# Compare remote vs local pin counts (shows terse table)
python pinterest_browser_export.py --compare-counts

# Update all boards with new remote pins
python pinterest_browser_export.py --update-boards all

# Update specific boards with new remote pins
python pinterest_browser_export.py --update-boards art,design

# Force update even if counts match
python pinterest_browser_export.py --update-boards all --force-update
```

### Analysis and Reports
```bash
# Generate download statistics
python pinterest_browser_export.py --stats

# Generate HTML gallery reports for all boards
python pinterest_browser_export.py --html-reports

# Generate HTML reports for specific boards
python pinterest_browser_export.py --html-reports studio,fun,electronics

# Download missing images
python pinterest_browser_export.py --download-missing

# List pin URLs for manual verification
python pinterest_browser_export.py --list-pins "studio ghibli"

# Export pin list to CSV
python pinterest_browser_export.py --list-pins "studio ghibli" --format csv --output pins.csv

# Generate video pins report
python pinterest_browser_export.py --video-report
```

### Maintenance Operations
```bash
# Rescan pins to populate missing titles
python pinterest_browser_export.py --rescan-titles

# Rescan pins to populate missing descriptions
python pinterest_browser_export.py --rescan-descriptions

# Rescan specific boards for metadata
python pinterest_browser_export.py --boards studio --rescan-titles --rescan-descriptions

# Sync image data with JSON files
python pinterest_browser_export.py --sync-images
```

## Output Structure

```
pinterest_export/
├── images/                    # Downloaded images organized by board
│   ├── art-inspiration/
│   ├── home-decor/
│   └── ...
├── collections/              # JSON metadata files
│   ├── art-inspiration.json
│   ├── home-decor.json
│   └── ...
├── reports/                  # Generated HTML reports
│   ├── art-inspiration.html
│   └── ...
├── download_progress.json    # Resume tracking
└── pinterest_export.log     # Detailed logs
```

## Board Slugs

Board slugs are URL-friendly versions of board names used in commands:
- "Art & Design" → `art-design`
- "Home Décor Ideas" → `home-decor-ideas`
- "Travel Photography" → `travel-photography`

Find them in your Pinterest board URLs: `pinterest.com/username/board-slug/`

## Requirements

- Python 3.7+
- Chrome/Chromium browser
- See `requirements.txt` for Python dependencies

## Documentation

For detailed documentation, see the `docs/` folder:

- **[Installation Guide](docs/installation.md)** - Detailed setup instructions
- **[Configuration Guide](docs/configuration.md)** - All configuration options
- **[Usage Guide](docs/usage.md)** - Comprehensive usage examples and workflows
- **[Command Reference](docs/commands.md)** - Complete command-line reference
- **[Troubleshooting](docs/troubleshooting.md)** - Common issues and solutions
- **[Technical Details](docs/technical.md)** - How the tool works internally
- **[Development](docs/development.md)** - TODOs and development notes

## Getting Help

Run with `-h` or `--help` to see all available options:
```bash
python pinterest_browser_export.py --help
```

## Legal Notice

This tool is for personal use only. Respect Pinterest's Terms of Service and rate limits. The authors are not responsible for any misuse of this tool.