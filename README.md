# Pinterest Browser Exporter

A comprehensive Pinterest board and pin exporter that uses browser automation to download all your Pinterest content, including images and metadata. This tool bypasses API limitations by directly interacting with Pinterest's web interface.

## Features

- **Complete Board Export**: Downloads all pins from your Pinterest boards
- **Image Download**: Saves high-quality images locally with organized folder structure
- **Metadata Preservation**: Captures pin titles, descriptions, URLs, and board information
- **Resume Capability**: Automatically resumes interrupted downloads
- **Rate Limiting**: Configurable delays to avoid being blocked
- **Progress Tracking**: Detailed logging and progress reports
- **Multiple Export Modes**: Export all boards, specific boards, or generate reports
- **HTML Reports**: Generate beautiful gallery reports of your collections

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Authentication**:
   Copy the example configuration and add your Pinterest credentials:
   ```bash
   cp .env.browser.example .env.browser
   ```

   Edit `.env.browser` with your Pinterest login details:
   ```
   PINTEREST_EMAIL=<EMAIL>
   PINTEREST_PASSWORD=your-password
   PINTEREST_USERNAME=your-username
   ```

3. **Run the Exporter**:
   ```bash
   # Export all boards
   python pinterest_browser_export.py

   # Export specific boards
   python pinterest_browser_export.py --boards art,design,photography

   # Generate statistics
   python pinterest_browser_export.py --stats
   ```

## Usage Examples

### Basic Export Operations
```bash
# Export all boards
python pinterest_browser_export.py

# Export specific boards (use board slugs)
python pinterest_browser_export.py --boards art-inspiration,home-decor

# Skip certain boards during export
python pinterest_browser_export.py --skip photography,recipes

# Re-crawl a specific board (updates existing data)
python pinterest_browser_export.py --recrawl art-inspiration
```

### Analysis and Reports
```bash
# Generate download statistics
python pinterest_browser_export.py --stats

# Compare remote vs local pin counts (shows terse table)
python pinterest_browser_export.py --compare-counts

# Update all boards with new remote pins
python pinterest_browser_export.py --update-boards all

# Update specific boards with new remote pins
python pinterest_browser_export.py --update-boards art,design

# Force update even if counts match
python pinterest_browser_export.py --update-boards all --force-update

# Generate HTML gallery reports
python pinterest_browser_export.py --html-reports

# Check for missing images
python pinterest_browser_export.py --check-missing

# Generate video pins report
python pinterest_browser_export.py --video-report
```

### Maintenance Operations
```bash
# Rescan pins to populate missing titles
python pinterest_browser_export.py --rescan-titles

# Rescan pins to populate missing descriptions
python pinterest_browser_export.py --rescan-descriptions

# Sync image data with JSON files
python pinterest_browser_export.py --sync-images
```

## Configuration

The script uses `.env.browser` for configuration. Key settings include:

### Authentication
- `PINTEREST_EMAIL`: Your Pinterest login email
- `PINTEREST_PASSWORD`: Your Pinterest password
- `PINTEREST_USERNAME`: Your Pinterest username

### Browser Settings
- `BROWSER_HEADLESS`: Run browser in background (true/false)
- `BROWSER_VIEWPORT_WIDTH/HEIGHT`: Browser window size

### Rate Limiting
- `MAX_PINS_PER_MINUTE`: Limit download speed (0 = no limit)
- `ACTION_DELAY_MIN/MAX`: Delays between actions
- `DOWNLOAD_DELAY_MIN/MAX`: Delays between downloads

### Output
- `PINTEREST_OUTPUT_DIR`: Where to save files (default: pinterest_export)
- `PINTEREST_IMAGE_SIZE`: Image quality (original/600x/236x)

## Output Structure

```
pinterest_export/
├── images/                    # Downloaded images organized by board
│   ├── art-inspiration/
│   ├── home-decor/
│   └── ...
├── collections/              # JSON metadata files
│   ├── art-inspiration.json
│   ├── home-decor.json
│   └── ...
├── reports/                  # Generated HTML reports
│   ├── art-inspiration.html
│   └── ...
├── download_progress.json    # Resume tracking
└── pinterest_export.log     # Detailed logs
```

## Board Slugs

Board slugs are URL-friendly versions of board names used in commands:
- "Art & Design" → `art-design`
- "Home Décor Ideas" → `home-decor-ideas`
- "Travel Photography" → `travel-photography`

## Troubleshooting

### Common Issues

1. **Login Failed**: Check your credentials in `.env.browser`
2. **Browser Crashes**: Try running in headless mode or adjust viewport size
3. **Rate Limited**: Increase delays in configuration
4. **Missing Images**: Run `--check-missing` to identify issues

### Getting Help

Run with `-h` or `--help` to see all available options:
```bash
python pinterest_browser_export.py --help
```

## Requirements

- Python 3.7+
- Chrome/Chromium browser
- See `requirements.txt` for Python dependencies

## Legal Notice

This tool is for personal use only. Respect Pinterest's Terms of Service and rate limits. The authors are not responsible for any misuse of this tool.