# Pinterest Pin Exporter

This script allows you to export all your Pinterest pins, including images and metadata.

## Setup

1. Create a Pinterest App at https://developers.pinterest.com/
2. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```
3. Fill in your Pinterest credentials in `.env`:
   - `PINTEREST_APP_ID`: Your Pinterest App ID
   - `PINTEREST_APP_SECRET`: Your Pinterest App Secret
   - `PINTEREST_ACCESS_TOKEN`: Your Pinterest Access Token

4. Install required packages:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

Run the script:
```bash
python pinterest_exporter.py
```

The script will:
1. Download all your pins' metadata
2. Save images to `pinterest_export/images/`
3. Save complete metadata as JSON to `pinterest_export/pins_metadata.json`

## Output

- `pinterest_export/images/`: Contains all downloaded pin images
- `pinterest_export/pins_metadata.json`: JSON file with all pin metadata, including local image paths 