import os
import json
import time
import random
import logging
import signal
from pathlib import Path
from datetime import datetime, timedelta
from tqdm import tqdm
from dotenv import load_dotenv
from slugify import slugify
from collections import deque
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import sys
import argparse
import psutil

class NullRateLimiter:
    """A rate limiter that does nothing, used when no rate limiting is needed."""
    def __init__(self):
        self.max_per_minute = None
    
    def wait_if_needed(self):
        pass

class RateLimiter:
    def __init__(self, max_per_minute):
        self.max_per_minute = max_per_minute
        self.requests = deque()
    
    def wait_if_needed(self):
        if not self.max_per_minute:
            return
            
        now = datetime.now()
        minute_ago = now - timedelta(minutes=1)
        
        # Remove requests older than 1 minute
        while self.requests and self.requests[0] < minute_ago:
            self.requests.popleft()
        
        # If we've hit the rate limit, wait
        if len(self.requests) >= self.max_per_minute:
            sleep_time = (self.requests[0] + timedelta(minutes=1) - now).total_seconds()
            if sleep_time > 0:
                time.sleep(sleep_time)
            self.requests.popleft()
        
        self.requests.append(now)

class DownloadTracker:
    """Tracks download progress and handles resumability."""
    def __init__(self, output_dir):
        self.output_dir = Path(output_dir)
        self.progress_file = self.output_dir / 'download_progress.json'
        self.progress = {
            'completed_pins': set(),
            'failed_pins': {},  # pin_id -> {url, board, failure_count, last_error}
            'completed_boards': set(),
            'current_board': None
        }
        self.load_progress()
    
    def load_progress(self):
        """Load progress from file if it exists."""
        if self.progress_file.exists():
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.progress['completed_pins'] = set(data.get('completed_pins', []))
                self.progress['failed_pins'] = data.get('failed_pins', {})
                self.progress['completed_boards'] = set(data.get('completed_boards', []))
                self.progress['current_board'] = data.get('current_board')
    
    def save_progress(self):
        """Save current progress to file."""
        data = {
            'completed_pins': list(self.progress['completed_pins']),
            'failed_pins': self.progress['failed_pins'],
            'completed_boards': list(self.progress['completed_boards']),
            'current_board': self.progress['current_board']
        }
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def mark_pin_completed(self, pin_id, board_name):
        """Mark a pin as successfully downloaded."""
        self.progress['completed_pins'].add(pin_id)
        if pin_id in self.progress['failed_pins']:
            del self.progress['failed_pins'][pin_id]
        self.save_progress()
    
    def mark_pin_failed(self, pin_id, pin_data, error):
        """Mark a pin as failed with error details."""
        if pin_id not in self.progress['failed_pins']:
            self.progress['failed_pins'][pin_id] = {
                'url': pin_data.get('image_url'),
                'board': pin_data.get('board'),
                'failure_count': 0,
                'last_error': None
            }
        
        self.progress['failed_pins'][pin_id]['failure_count'] += 1
        self.progress['failed_pins'][pin_id]['last_error'] = str(error)
        self.save_progress()
    
    def mark_board_completed(self, board_name):
        """Mark a board as completely processed."""
        self.progress['completed_boards'].add(board_name)
        self.progress['current_board'] = None
        self.save_progress()
    
    def set_current_board(self, board_name):
        """Set the currently processing board."""
        self.progress['current_board'] = board_name
        self.save_progress()
    
    def should_process_pin(self, pin_id):
        """Determine if a pin should be processed."""
        if pin_id in self.progress['completed_pins']:
            return False
        if pin_id in self.progress['failed_pins']:
            return self.progress['failed_pins'][pin_id]['failure_count'] < 2
        return True
    
    def should_process_board(self, board_name):
        """Determine if a board should be processed."""
        return board_name not in self.progress['completed_boards']

class PinterestBrowserExporter:
    def __init__(self, email, password, username, output_dir="pinterest_export", rate_limit=None):
        """Initialize the Pinterest exporter."""
        self.email = email
        self.password = password
        self.username = username
        self.output_dir = Path(output_dir)
        self.images_dir = self.output_dir / "images"
        self.images_dir.mkdir(parents=True, exist_ok=True)
        self.tracker = DownloadTracker(output_dir)
        self.rate_limiter = RateLimiter(rate_limit) if rate_limit else NullRateLimiter()
        self.logger = logging.getLogger(__name__)
        
        # Browser configuration
        self.headless = os.getenv('BROWSER_HEADLESS', 'false').lower() == 'true'
        self.viewport_width = int(os.getenv('BROWSER_VIEWPORT_WIDTH', '1380'))
        self.viewport_height = int(os.getenv('BROWSER_VIEWPORT_HEIGHT', '880'))
        
        # Rate limiting configuration
        self.action_delay_min = float(os.getenv('ACTION_DELAY_MIN', '2.0'))
        self.action_delay_max = float(os.getenv('ACTION_DELAY_MAX', '5.0'))
        
        self.scroll_delay_min = float(os.getenv('SCROLL_DELAY_MIN', '1.0'))
        self.scroll_delay_max = float(os.getenv('SCROLL_DELAY_MAX', '3.0'))
        self.scroll_overlap_min = 0.1  # 10% overlap minimum
        self.scroll_overlap_max = 0.2  # 20% overlap maximum
        self.scroll_check_timeout = int(os.getenv('SCROLL_CHECK_TIMEOUT', '600'))  # Increased to 10 minutes
        self.scroll_progress_interval = int(os.getenv('SCROLL_PROGRESS_INTERVAL', '30'))  # Log progress every 30 seconds
        self.scroll_pause_chance = float(os.getenv('SCROLL_PAUSE_CHANCE', '0.1'))  # 10% chance of pause
        self.scroll_pause_min = float(os.getenv('SCROLL_PAUSE_MIN', '1.5'))
        self.scroll_pause_max = float(os.getenv('SCROLL_PAUSE_MAX', '3.0'))
        
        self.download_delay_min = float(os.getenv('DOWNLOAD_DELAY_MIN', '3.0'))
        self.download_delay_max = float(os.getenv('DOWNLOAD_DELAY_MAX', '7.0'))
        self.download_timeout = int(os.getenv('DOWNLOAD_TIMEOUT', '30'))
        
        # Image configuration
        self.image_size = os.getenv('PINTEREST_IMAGE_SIZE', 'original')
        
        # Retry configuration
        self.max_retries = int(os.getenv('MAX_RETRIES', '3'))
        self.retry_delay = int(os.getenv('RETRY_DELAY', '5'))
        
        # Output configuration
        self.save_failed_pins = os.getenv('SAVE_FAILED_PINS', 'true').lower() == 'true'
        self.organize_by_board = True  # Always organize by board in this version
        
        # Selectors
        self.selectors = {
            'email_input': 'input[id="email"]',
            'password_input': 'input[id="password"]',
            'login_button': 'button[type="submit"]',
            'home': 'div[data-test-id="homefeed-feed"]',
            # Board page selectors
            'board_grid': '[data-test-id="masonry-container"]',
            'board_link': '[data-test-id="masonry-container"] a',
            'board_summary': '[data-test-id="board-summary-container"]',
            'board_pin_count': '[data-test-id="pin-count"]',
            # Pin grid selectors
            'pin_grid': '[data-test-id="board-feed"]',
            'pin_link': '[data-test-id="pin"] a',
            # Pin page selectors
            'pin_image': '[data-test-id="closeup-body-image-container"] img',
            'pin_title': '[data-test-id="pin-title"]',
            'pin_description': '[data-test-id="pin-description"]'
        }
        
        # Log configuration
        self.logger.info("\nConfiguration:")
        self.logger.info(f"Output directory: {self.output_dir}")
        self.logger.info(f"Browser mode: {'headless' if self.headless else 'visible'}")
        self.logger.info(f"Viewport: {self.viewport_width}x{self.viewport_height}")
        self.logger.info(f"Image size: {self.image_size}")
        self.logger.info("\nRate Limiting Configuration:")
        self.logger.info(f"Action delays: {self.action_delay_min}-{self.action_delay_max}s")
        self.logger.info(f"Scroll delays: {self.scroll_delay_min}-{self.scroll_delay_max}s")
        self.logger.info(f"Download delays: {self.download_delay_min}-{self.download_delay_max}s")
        if self.rate_limiter.max_per_minute:
            self.logger.info(f"Max pins per minute: {self.rate_limiter.max_per_minute}")
        else:
            self.logger.info("No pin rate limiting")
        
        # Initialize collections
        self.pins = []
        self.downloaded_images = set()
        
        # Initialize WebDriver
        self.driver = None
        
        # Set up signal handling
        signal.signal(signal.SIGINT, self._handle_interrupt)
        self._interrupted = False
        
        # Track processed tall pins across scrolls
        self.processed_tall_pins = set()

    def _handle_interrupt(self, signum, frame):
        """Handle interrupt signal (Ctrl+C)."""
        if self._interrupted:  # If interrupted twice, exit immediately
            print("\nForced exit.")  # Use print for immediate output
            sys.exit(1)
        
        self._interrupted = True
        print("\nGracefully shutting down...")  # Use print for immediate output
        self.cleanup()
        sys.exit(0)
    
    def cleanup(self):
        """Clean up resources and save progress."""
        try:
            # Save progress first
            if hasattr(self, 'tracker'):
                self.tracker.save_progress()
            
            # Then try to close the browser
            if self.driver:
                # Temporarily redirect stderr to suppress connection error messages
                with open(os.devnull, 'w') as devnull:
                    stderr_backup = sys.stderr
                    sys.stderr = devnull
                    try:
                        # First try to close all windows
                        try:
                            for handle in self.driver.window_handles:
                                try:
                                    self.driver.switch_to.window(handle)
                                    self.driver.close()
                                except:
                                    pass
                        except:
                            pass
                        
                        # Then quit the driver
                        try:
                            self.driver.quit()
                        except:
                            pass
                    finally:
                        sys.stderr = stderr_backup
                
            # Force cleanup of any remaining Chrome processes using our profile
            try:
                user_data_dir = self.output_dir / "chrome_profile"
                for proc in psutil.process_iter(['name', 'cmdline']):
                    try:
                        if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                            cmdline = proc.info.get('cmdline', [])
                            if any(str(user_data_dir) in arg for arg in cmdline):
                                try:
                                    proc.terminate()
                                    try:
                                        proc.wait(timeout=1)
                                    except:
                                        try:
                                            proc.kill()
                                        except:
                                            pass
                                except:
                                    pass
                    except:
                        pass
            except:
                pass
                
            # Small delay to ensure cleanup is complete
            time.sleep(1)
            
        except:
            # Silently ignore any errors during cleanup
            pass

    def setup_driver(self):
        """Set up the Chrome WebDriver with appropriate options."""
        try:
            # First try to clean up any existing Chrome processes using this profile
            user_data_dir = self.output_dir / "chrome_profile"
            user_data_dir.mkdir(parents=True, exist_ok=True)
            
            # Try to close any existing Chrome instances using this profile
            for proc in psutil.process_iter(['name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info.get('cmdline', [])
                        if any(str(user_data_dir) in arg for arg in cmdline):
                            self.logger.info(f"Found existing Chrome process using our profile, terminating...")
                            proc.terminate()
                            proc.wait(timeout=5)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    pass
            
            # Small delay to ensure cleanup is complete
            time.sleep(2)
            
            chrome_options = Options()
            chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
            
            if self.headless:
                chrome_options.add_argument('--headless=new')
            
            # Add arguments for exact viewport size
            chrome_options.add_argument('--window-size=1380,880')
            chrome_options.add_argument('--hide-scrollbars')  # Hide scrollbars to ensure exact viewport size
            chrome_options.add_argument('--force-device-scale-factor=1')  # Ensure 1:1 pixel ratio
            
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            
            # Let Selenium find the driver automatically
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # Force window size after creation and verify viewport size
            self.driver.set_window_size(self.viewport_width, self.viewport_height)
            
            # Execute JavaScript to set viewport size exactly
            self.driver.execute_script("""
                window.resizeTo(0, 0);
                window.outerWidth = arguments[0];
                window.outerHeight = arguments[1];
            """, self.viewport_width, self.viewport_height)
            
            # Verify viewport dimensions
            actual_width = self.driver.execute_script('return window.innerWidth;')
            actual_height = self.driver.execute_script('return window.innerHeight;')
            
            if actual_width != self.viewport_width or actual_height != self.viewport_height:
                self.logger.warning(f"Viewport size mismatch! Wanted: {self.viewport_width}x{self.viewport_height}, Got: {actual_width}x{actual_height}")
                # Try one more time with adjusted size to account for any browser chrome
                adjusted_width = self.viewport_width + (self.viewport_width - actual_width)
                adjusted_height = self.viewport_height + (self.viewport_height - actual_height)
                self.driver.set_window_size(adjusted_width, adjusted_height)
                
                actual_width = self.driver.execute_script('return window.innerWidth;')
                actual_height = self.driver.execute_script('return window.innerHeight;')
                
            self.logger.info(f"Browser viewport size: {actual_width}x{actual_height}")
            
            self.wait = WebDriverWait(self.driver, 10)
            self.logger.info("WebDriver initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing WebDriver: {str(e)}")
            raise

    def is_logged_in(self):
        """Check if we're already logged into Pinterest."""
        try:
            self.driver.get("https://www.pinterest.com")
            # Wait briefly for the page to load
            time.sleep(2)
            
            # Check for elements that indicate we're logged in
            try:
                # Look for the home feed or header avatar
                self.driver.find_element(By.CSS_SELECTOR, self.selectors['home'])
                return True
            except NoSuchElementException:
                # Look for login button as a fallback
                try:
                    self.driver.find_element(By.CSS_SELECTOR, '[data-test-id="simple-login-button"]')
                    return False
                except NoSuchElementException:
                    # If neither is found, assume we're not logged in
                    return False
        except Exception as e:
            self.logger.warning(f"Error checking login status: {e}")
            return False
    
    def login(self):
        """Log in to Pinterest if not already logged in."""
        self.logger.info("Checking login status...")
        
        if self.is_logged_in():
            self.logger.info("Already logged in to Pinterest")
            return
        
        self.logger.info("Not logged in, proceeding with login...")
        try:
            # Navigate to login page
            self.driver.get("https://www.pinterest.com/login/")
            
            # Wait for and fill in email field
            email_field = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['email_input'])))
            email_field.send_keys(self.email)
            self.random_delay()
            
            # Wait for and fill in password field
            password_field = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['password_input'])))
            password_field.send_keys(self.password)
            self.random_delay()
            
            # Wait for login button and click it using JavaScript
            login_button = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['login_button'])))
            self.driver.execute_script("arguments[0].click();", login_button)
            
            # Wait for login to complete
            self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['home'])))
            self.logger.info("Successfully logged in")
            
        except Exception as e:
            self.logger.error(f"Login failed: {str(e)}")
            raise
    
    def random_delay(self, min_delay=None, max_delay=None):
        """Add random delay between actions to mimic human behavior."""
        min_delay = min_delay or self.action_delay_min
        max_delay = max_delay or self.action_delay_max
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def wait_for_element(self, selector, timeout=10):
        """Wait for an element to be present and visible."""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, selector))
            )
            return element
        except TimeoutException:
            return None
    
    def scroll_to_bottom(self, start_position=0):
        """Scroll to bottom of page with natural, random delays and pauses."""
        # Always start from the top of the page
        self.driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)  # Wait for initial content to load
        
        start_time = time.time()
        last_height = self.driver.execute_script("return document.documentElement.scrollHeight")
        no_change_count = 0
        scroll_position = 0  # Always start from top
        last_progress_time = start_time
        initial_height = last_height
        last_content_load_time = start_time
        pin_urls = set()  # Set of tuples (url, aria-label)
        duplicate_count = 0  # Track number of duplicates found
        seen_urls = {}  # Track URLs and where they were first seen
        seen_pin_ids = {}  # Track pin IDs separately
        expected_pins = None
        last_unique_count = 0  # Track when we last found new pins
        no_new_pins_count = 0  # Track consecutive scrolls without new pins
        
        # Try to get the expected pin count from the board summary
        try:
            pin_count_element = None
            for selector in [self.selectors['board_pin_count'], 'h2 span']:
                try:
                    pin_count_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if pin_count_element and 'pin' in pin_count_element.text.lower():
                        break
                except NoSuchElementException:
                    continue
            
            if pin_count_element:
                pin_count_text = pin_count_element.text.strip().lower()
                # Handle both singular and plural forms
                pin_count_text = pin_count_text.split()[0]  # Get just the number part
                if 'k' in pin_count_text:
                    expected_pins = int(float(pin_count_text.replace('k', '')) * 1000)
                else:
                    # Remove commas and convert to integer
                    pin_count_text = pin_count_text.replace(',', '')
                    expected_pins = int(pin_count_text)
                self.logger.info(f"Board states it has {expected_pins} pins")
            else:
                expected_pins = None
                self.logger.warning("Could not find pin count in board summary")
        except Exception as e:
            self.logger.warning(f"Error getting pin count from board summary: {e}")
        
        self.logger.info(f"Starting scroll with initial page height: {initial_height}px")
        
        while True:
            current_time = time.time()
            elapsed_time = current_time - start_time
            
            # Get current scroll height
            current_height = self.driver.execute_script("return document.documentElement.scrollHeight")
            if current_height != last_height:
                self.logger.debug(f"Page height changed: {last_height}px -> {current_height}px")
                last_height = current_height
                last_content_load_time = current_time
                no_change_count = 0
            
            # Log progress periodically
            if current_time - last_progress_time >= self.scroll_progress_interval:
                progress_pct = (scroll_position / current_height) * 100 if current_height > 0 else 0
                self.logger.info(f"Scroll progress: {scroll_position}px of {current_height}px ({progress_pct:.1f}%) - {elapsed_time:.0f}s elapsed")
                if expected_pins:
                    self.logger.info(f"Pin collection progress: {len(pin_urls)} of {expected_pins} expected pins found ({(len(pin_urls)/expected_pins)*100:.1f}%)")
                else:
                    self.logger.info(f"Unique pins found: {len(pin_urls)}, Duplicates found: {duplicate_count}")
                last_progress_time = current_time
            
            # Calculate scroll amount based on viewport height with overlap
            overlap_percent = random.uniform(self.scroll_overlap_min, self.scroll_overlap_max)
            overlap_pixels = int(self.viewport_height * overlap_percent)
            scroll_amount = self.viewport_height - overlap_pixels
            
            # Get current scroll position before scrolling
            current_scroll = self.driver.execute_script("return window.pageYOffset;")
            
            # Scroll to new position with smooth behavior
            self.driver.execute_script("""
                window.scrollTo({
                    top: arguments[0],
                    behavior: 'smooth'
                });
            """, scroll_position + scroll_amount)
            
            # Wait for content to load after scrolling
            time.sleep(1)
            
            # Get new scroll position and calculate actual scroll distance
            new_scroll = self.driver.execute_script("return window.pageYOffset;")
            actual_scroll_distance = new_scroll - current_scroll
            
            # If we barely scrolled (less than 10% of intended distance), we've hit the bottom
            if actual_scroll_distance < (scroll_amount * 0.1):
                no_change_count += 1
                self.logger.info(f"Reached bottom of page - only scrolled {actual_scroll_distance}px of intended {scroll_amount}px")
                # If we haven't found all pins, move to next pass
                if expected_pins and len(pin_urls) < expected_pins:
                    self.logger.info(f"\nStill missing {expected_pins - len(pin_urls)} pins")
                    self.logger.info("Starting next scroll pass...")
                    break
            else:
                scroll_position = new_scroll
                no_change_count = 0
            
            # After each scroll, collect pins
            self.logger.info(f"Collecting pins at scroll position {scroll_position}px")
            
            # Track the number of unique pins before collection
            pins_before = len(pin_urls)
            
            # Collect ALL pins in the current viewport
            pin_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-test-id="masonry-container"] [data-test-id="pin"] a, [data-test-id="masonry-container"] [data-test-id="pinWrapper"] a, [data-test-id="masonry-container"] [role="listitem"] a, [data-test-id="masonry-container"] [data-grid-item] a[href*="/pin/"]')
            
            # Also try alternate selectors if we found no pins
            if not pin_elements:
                pin_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-test-id="masonry-container"] div[style*="position: absolute"] a[href*="/pin/"]')
            
            # Check for tall pins and handle them
            for pin in pin_elements:
                try:
                    # Get the pin's parent element to check its height
                    parent = pin.find_element(By.XPATH, '..')
                    pin_height = self.driver.execute_script("return arguments[0].offsetHeight;", parent)
                    
                    # If pin is taller than viewport, scroll past it
                    if pin_height > self.viewport_height:
                        # Get the pin's URL to use as unique identifier
                        url = pin.get_attribute('href')
                        if url and url not in self.processed_tall_pins:
                            self.logger.info(f"Found tall pin (height: {pin_height}px) - scrolling past it")
                            self.processed_tall_pins.add(url)
                            
                            # Get the pin's position
                            pin_top = self.driver.execute_script("return arguments[0].getBoundingClientRect().top;", parent)
                            pin_bottom = pin_top + pin_height
                            
                            # Scroll to the bottom of the pin
                            self.driver.execute_script("window.scrollTo(0, arguments[0]);", scroll_position + pin_bottom)
                            time.sleep(2)  # Wait for content to load
                            
                            # Scroll back up to a screen above the pin
                            self.driver.execute_script("window.scrollTo(0, arguments[0]);", scroll_position + pin_top - self.viewport_height)
                            time.sleep(2)  # Wait for content to load
                        else:
                            self.logger.debug(f"Skipping already processed tall pin (height: {pin_height}px)")
                        
                        # Continue with normal scrolling
                        continue
                        
                    url = pin.get_attribute('href')
                    if url and '/pin/' in url:
                        # Extract pin ID from URL
                        pin_id = url.split('/pin/')[1].split('/')[0]
                        
                        # Get additional pin info
                        aria_label = pin.get_attribute('aria-label') or ''
                        title = ''
                        
                        # Try to get title from parent elements
                        try:
                            parent = pin.find_element(By.XPATH, '..')
                            title_elem = parent.find_element(By.CSS_SELECTOR, '[data-test-id="pin-title"]')
                            title = title_elem.text.strip()
                        except:
                            pass
                            
                        pin_data = (url, title or aria_label)
                        
                        # Check for duplicates by both URL and pin ID
                        if url in seen_urls:
                            duplicate_count += 1
                            self.logger.debug(f"Duplicate pin found by URL at position {scroll_position}px: {url}")
                            self.logger.debug(f"First seen at position: {seen_urls[url]}px")
                            continue
                            
                        if pin_id in seen_pin_ids:
                            duplicate_count += 1
                            self.logger.debug(f"Duplicate pin found by ID at position {scroll_position}px: {pin_id}")
                            self.logger.debug(f"First seen at position: {seen_pin_ids[pin_id]}px")
                            continue
                        
                        # If it's a new pin, add it to both tracking sets
                        seen_urls[url] = scroll_position
                        seen_pin_ids[pin_id] = scroll_position
                        pin_urls.add(pin_data)
                        self.logger.debug(f"New pin added: {url} (ID: {pin_id})")
                except Exception as e:
                    self.logger.debug(f"Error processing pin element: {e}")
                    continue
            
            # Check if we found any new pins in this scroll
            pins_after = len(pin_urls)
            if pins_after > pins_before:
                no_new_pins_count = 0  # Reset counter when we find new pins
                self.logger.info(f"Found {pins_after - pins_before} new unique pins")
            else:
                no_new_pins_count += 1
                self.logger.info("No new unique pins found in this scroll")
            
            # Log progress after each collection
            self.logger.info(f"Total: {len(pin_urls)} unique pins, {duplicate_count} duplicates")
            
            # Validate against expected pin count
            if expected_pins and len(pin_urls) > expected_pins:
                self.logger.warning(f"Found more pins ({len(pin_urls)}) than expected ({expected_pins})")
                self.logger.warning("This might indicate duplicate pins slipping through - performing additional validation")
                
                # Additional validation of collected pins
                validated_pins = set()
                validated_pin_ids = set()
                for url, title in pin_urls:
                    pin_id = url.split('/pin/')[1].split('/')[0]
                    if pin_id not in validated_pin_ids:
                        validated_pins.add((url, title))
                        validated_pin_ids.add(pin_id)
                    else:
                        self.logger.warning(f"Found duplicate pin ID during validation: {pin_id}")
                
                # Update pin_urls with validated set
                pin_urls = validated_pins
                self.logger.info(f"After validation: {len(pin_urls)} unique pins")
            
            # Check if we've found all expected pins
            if expected_pins and len(pin_urls) >= expected_pins:
                self.logger.info(f"Found all {expected_pins} expected pins!")
                break
            
            # Random pause (10% chance)
            if random.random() < self.scroll_pause_chance:
                pause_time = random.uniform(self.scroll_pause_min, self.scroll_pause_max)
                self.logger.debug(f"Random pause for {pause_time:.1f} seconds")
                time.sleep(pause_time)
            
            # Regular delay between scrolls
            self.random_delay(self.scroll_delay_min, self.scroll_delay_max)
            
            # Break conditions:
            # 1. We've hit the bottom AND haven't found new pins in several scrolls
            # 2. We've scrolled multiple times without finding new pins
            if scroll_position >= current_height - 1000:
                self.random_delay(2.0, 3.0)
                
                # Check if more content loaded
                new_height = self.driver.execute_script("return document.documentElement.scrollHeight")
                if new_height == current_height:
                    no_change_count += 1
                else:
                    no_change_count = 0
                    last_content_load_time = current_time
                    
                if no_change_count >= 3 and no_new_pins_count >= 3:
                    if not expected_pins or len(pin_urls) >= expected_pins:
                        break
                    else:
                        # Try scrolling back up a bit to trigger more loading
                        scroll_position = max(0, scroll_position - 2000)  # Scroll back further
                        self.driver.execute_script("window.scrollTo(0, arguments[0]);", scroll_position)
                        time.sleep(3)  # Wait longer for content to load
                        no_change_count = 0
                        no_new_pins_count = 0
                        
                        # Try to force a refresh of the viewport
                        self.driver.execute_script("window.dispatchEvent(new Event('resize'));")
                        time.sleep(1)
        
        # If we haven't found all expected pins, do a full page scan
        if expected_pins and len(pin_urls) < expected_pins:
            self.logger.info(f"\nFound only {len(pin_urls)} of {expected_pins} expected pins")
            self.logger.info("Starting second scroll pass from top of page...")
            
            # Reset scroll position and counters
            scroll_position = 0
            no_change_count = 0
            no_new_pins_count = 0
            last_content_load_time = time.time()
            
            # Scroll back to top
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(3)  # Wait longer for content to reset
            
            # Start second scroll pass
            while True:
                current_time = time.time()
                elapsed_time = current_time - start_time
                
                # Get current scroll height
                current_height = self.driver.execute_script("return document.documentElement.scrollHeight")
                if current_height != last_height:
                    self.logger.debug(f"Page height changed: {last_height}px -> {current_height}px")
                    last_height = current_height
                    last_content_load_time = current_time
                    no_change_count = 0
                
                # Log progress periodically
                if current_time - last_progress_time >= self.scroll_progress_interval:
                    progress_pct = (scroll_position / current_height) * 100 if current_height > 0 else 0
                    self.logger.info(f"Second pass - Scroll progress: {scroll_position}px of {current_height}px ({progress_pct:.1f}%) - {elapsed_time:.0f}s elapsed")
                    self.logger.info(f"Pin collection progress: {len(pin_urls)} of {expected_pins} expected pins found ({(len(pin_urls)/expected_pins)*100:.1f}%)")
                    last_progress_time = current_time
                
                # Calculate scroll amount based on viewport height with overlap
                overlap_percent = random.uniform(self.scroll_overlap_min, self.scroll_overlap_max)
                overlap_pixels = int(self.viewport_height * overlap_percent)
                scroll_amount = self.viewport_height - overlap_pixels
                
                # Update scroll position, ensuring we don't go beyond the page height
                new_scroll_position = min(scroll_position + scroll_amount, current_height)
                
                # Check if we can actually scroll further
                if new_scroll_position <= scroll_position:
                    no_change_count += 1
                    self.logger.info("Reached bottom of page - no more scrolling possible")
                    # If we haven't found all pins, move to next pass
                    if expected_pins and len(pin_urls) < expected_pins:
                        self.logger.info(f"\nStill missing {expected_pins - len(pin_urls)} pins")
                        self.logger.info("Starting next scroll pass...")
                        break
                else:
                    scroll_position = new_scroll_position
                    no_change_count = 0
                
                # Scroll to new position with smooth behavior
                self.driver.execute_script("""
                    window.scrollTo({
                        top: arguments[0],
                        behavior: 'smooth'
                    });
                """, scroll_position)
                
                # Wait for content to load after scrolling
                time.sleep(1)
                
                # After each scroll, collect pins
                self.logger.info(f"Second pass - Collecting pins at scroll position {scroll_position}px")
                
                # Track the number of unique pins before collection
                pins_before = len(pin_urls)
                
                # Collect ALL pins in the current viewport
                pin_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-test-id="masonry-container"] [data-test-id="pin"] a, [data-test-id="masonry-container"] [data-test-id="pinWrapper"] a, [data-test-id="masonry-container"] [role="listitem"] a, [data-test-id="masonry-container"] [data-grid-item] a[href*="/pin/"]')
                # Also try alternate selectors if we found no pins
                if not pin_elements:
                    pin_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-test-id="masonry-container"] div[style*="position: absolute"] a[href*="/pin/"]')
                
                # Check for tall pins and handle them
                for pin in pin_elements:
                    try:
                        # Get the pin's parent element to check its height
                        parent = pin.find_element(By.XPATH, '..')
                        pin_height = self.driver.execute_script("return arguments[0].offsetHeight;", parent)
                        
                        # If pin is taller than viewport, scroll past it
                        if pin_height > self.viewport_height:
                            # Get the pin's URL to use as unique identifier
                            url = pin.get_attribute('href')
                            if url and url not in self.processed_tall_pins:
                                self.logger.info(f"Found tall pin (height: {pin_height}px) - scrolling past it")
                                self.processed_tall_pins.add(url)
                                
                                # Get the pin's position
                                pin_top = self.driver.execute_script("return arguments[0].getBoundingClientRect().top;", parent)
                                pin_bottom = pin_top + pin_height
                                
                                # Scroll to the bottom of the pin
                                self.driver.execute_script("window.scrollTo(0, arguments[0]);", scroll_position + pin_bottom)
                                time.sleep(2)  # Wait for content to load
                                
                                # Scroll back up to a screen above the pin
                                self.driver.execute_script("window.scrollTo(0, arguments[0]);", scroll_position + pin_top - self.viewport_height)
                                time.sleep(2)  # Wait for content to load
                            else:
                                self.logger.debug(f"Skipping already processed tall pin (height: {pin_height}px)")
                            
                            # Continue with normal scrolling
                            continue
                            
                        url = pin.get_attribute('href')
                        if url and '/pin/' in url:
                            # Extract pin ID from URL
                            pin_id = url.split('/pin/')[1].split('/')[0]
                            
                            # Get additional pin info
                            aria_label = pin.get_attribute('aria-label') or ''
                            title = ''
                            
                            # Try to get title from parent elements
                            try:
                                parent = pin.find_element(By.XPATH, '..')
                                title_elem = parent.find_element(By.CSS_SELECTOR, '[data-test-id="pin-title"]')
                                title = title_elem.text.strip()
                            except:
                                pass
                                
                            pin_data = (url, title or aria_label)
                            
                            # Check for duplicates by both URL and pin ID
                            if url in seen_urls:
                                duplicate_count += 1
                                self.logger.debug(f"Duplicate pin found by URL at position {scroll_position}px: {url}")
                                self.logger.debug(f"First seen at position: {seen_urls[url]}px")
                                continue
                                
                            if pin_id in seen_pin_ids:
                                duplicate_count += 1
                                self.logger.debug(f"Duplicate pin found by ID at position {scroll_position}px: {pin_id}")
                                self.logger.debug(f"First seen at position: {seen_pin_ids[pin_id]}px")
                                continue
                            
                            # If it's a new pin, add it to both tracking sets
                            seen_urls[url] = scroll_position
                            seen_pin_ids[pin_id] = scroll_position
                            pin_urls.add(pin_data)
                            self.logger.debug(f"New pin added: {url} (ID: {pin_id})")
                    except Exception as e:
                        self.logger.debug(f"Error processing pin element: {e}")
                        continue
                
                # Check if we found any new pins in this scroll
                pins_after = len(pin_urls)
                if pins_after > pins_before:
                    no_new_pins_count = 0  # Reset counter when we find new pins
                    self.logger.info(f"Found {pins_after - pins_before} new unique pins")
                else:
                    no_new_pins_count += 1
                    self.logger.info("No new unique pins found in this scroll")
                
                # Log progress after each collection
                self.logger.info(f"Total: {len(pin_urls)} unique pins, {duplicate_count} duplicates")
                
                # Check if we've found all expected pins
                if len(pin_urls) >= expected_pins:
                    self.logger.info(f"Found all {expected_pins} expected pins!")
                    break
                
                # Random pause (10% chance)
                if random.random() < self.scroll_pause_chance:
                    pause_time = random.uniform(self.scroll_pause_min, self.scroll_pause_max)
                    self.logger.debug(f"Random pause for {pause_time:.1f} seconds")
                    time.sleep(pause_time)
                
                # Regular delay between scrolls
                self.random_delay(self.scroll_delay_min, self.scroll_delay_max)
                
                # Break if we've hit the bottom again
                if scroll_position >= current_height - 1000:
                    self.random_delay(2.0, 3.0)
                    
                    # Check if more content loaded
                    new_height = self.driver.execute_script("return document.documentElement.scrollHeight")
                    if new_height == current_height:
                        no_change_count += 1
                    else:
                        no_change_count = 0
                        last_content_load_time = current_time
                        
                    if no_change_count >= 3 and no_new_pins_count >= 3:
                        # If we haven't found all pins, try one final pass
                        if expected_pins and len(pin_urls) < expected_pins:
                            self.logger.info(f"\nStill missing {expected_pins - len(pin_urls)} pins after two passes")
                            self.logger.info("Attempting final pass with slower scrolling...")
                            
                            # Reset scroll position and counters
                            scroll_position = 0
                            no_change_count = 0
                            no_new_pins_count = 0
                            last_content_load_time = time.time()
                            
                            # Scroll back to top
                            self.driver.execute_script("window.scrollTo(0, 0);")
                            time.sleep(3)  # Wait longer for content to reset
                            
                            # Final pass with slower scrolling
                            while True:
                                current_time = time.time()
                                elapsed_time = current_time - start_time
                                
                                # Get current scroll height
                                current_height = self.driver.execute_script("return document.documentElement.scrollHeight")
                                if current_height != last_height:
                                    self.logger.debug(f"Page height changed: {last_height}px -> {current_height}px")
                                    last_height = current_height
                                    last_content_load_time = current_time
                                    no_change_count = 0
                                
                                # Log progress periodically
                                if current_time - last_progress_time >= self.scroll_progress_interval:
                                    progress_pct = (scroll_position / current_height) * 100 if current_height > 0 else 0
                                    self.logger.info(f"Final pass - Scroll progress: {scroll_position}px of {current_height}px ({progress_pct:.1f}%) - {elapsed_time:.0f}s elapsed")
                                    self.logger.info(f"Pin collection progress: {len(pin_urls)} of {expected_pins} expected pins found ({(len(pin_urls)/expected_pins)*100:.1f}%)")
                                    last_progress_time = current_time
                                
                                # Calculate scroll amount with more overlap for final pass
                                overlap_percent = random.uniform(0.5, 0.7)  # More overlap in final pass
                                overlap_pixels = int(self.viewport_height * overlap_percent)
                                scroll_amount = self.viewport_height - overlap_pixels
                                
                                # Update scroll position, ensuring we don't go beyond the page height
                                new_scroll_position = min(scroll_position + scroll_amount, current_height)
                                if new_scroll_position == scroll_position:
                                    no_change_count += 1
                                    self.logger.info("Reached bottom of page in final pass - no more scrolling possible")
                                else:
                                    scroll_position = new_scroll_position
                                    no_change_count = 0
                                
                                # Scroll to new position with smooth behavior
                                self.driver.execute_script("""
                                    window.scrollTo({
                                        top: arguments[0],
                                        behavior: 'smooth'
                                    });
                                """, scroll_position)
                                
                                # Wait longer for content to load in final pass
                                time.sleep(2)
                                
                                # After each scroll, collect pins
                                self.logger.info(f"Final pass - Collecting pins at scroll position {scroll_position}px")
                                
                                # Track the number of unique pins before collection
                                pins_before = len(pin_urls)
                                
                                # Collect ALL pins in the current viewport
                                pin_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-test-id="masonry-container"] [data-test-id="pin"] a, [data-test-id="masonry-container"] [data-test-id="pinWrapper"] a, [data-test-id="masonry-container"] [role="listitem"] a, [data-test-id="masonry-container"] [data-grid-item] a[href*="/pin/"]')
                                
                                # Also try alternate selectors if we found no pins
                                if not pin_elements:
                                    pin_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-test-id="masonry-container"] div[style*="position: absolute"] a[href*="/pin/"]')
                                
                                # Check for tall pins and handle them
                                for pin in pin_elements:
                                    try:
                                        # Get the pin's parent element to check its height
                                        parent = pin.find_element(By.XPATH, '..')
                                        pin_height = self.driver.execute_script("return arguments[0].offsetHeight;", parent)
                                        
                                        # If pin is taller than viewport, scroll past it
                                        if pin_height > self.viewport_height:
                                            # Get the pin's URL to use as unique identifier
                                            url = pin.get_attribute('href')
                                            if url and url not in self.processed_tall_pins:
                                                self.logger.info(f"Found tall pin (height: {pin_height}px) - scrolling past it")
                                                self.processed_tall_pins.add(url)
                                                
                                                # Get the pin's position
                                                pin_top = self.driver.execute_script("return arguments[0].getBoundingClientRect().top;", parent)
                                                pin_bottom = pin_top + pin_height
                                                
                                                # Scroll to the bottom of the pin
                                                self.driver.execute_script("window.scrollTo(0, arguments[0]);", scroll_position + pin_bottom)
                                                time.sleep(2)  # Wait for content to load
                                                
                                                # Scroll back up to a screen above the pin
                                                self.driver.execute_script("window.scrollTo(0, arguments[0]);", scroll_position + pin_top - self.viewport_height)
                                                time.sleep(2)  # Wait for content to load
                                            else:
                                                self.logger.debug(f"Skipping already processed tall pin (height: {pin_height}px)")
                                            
                                            # Continue with normal scrolling
                                            continue
                                            
                                        url = pin.get_attribute('href')
                                        if url and '/pin/' in url:
                                            # Extract pin ID from URL
                                            pin_id = url.split('/pin/')[1].split('/')[0]
                                            
                                            # Get additional pin info
                                            aria_label = pin.get_attribute('aria-label') or ''
                                            title = ''
                                            
                                            # Try to get title from parent elements
                                            try:
                                                parent = pin.find_element(By.XPATH, '..')
                                                title_elem = parent.find_element(By.CSS_SELECTOR, '[data-test-id="pin-title"]')
                                                title = title_elem.text.strip()
                                            except:
                                                pass
                                            
                                            pin_data = (url, title or aria_label)
                                            
                                            # Check for duplicates by both URL and pin ID
                                            if url in seen_urls:
                                                duplicate_count += 1
                                                self.logger.debug(f"Duplicate pin found by URL at position {scroll_position}px: {url}")
                                                self.logger.debug(f"First seen at position: {seen_urls[url]}px")
                                                continue
                                                
                                            if pin_id in seen_pin_ids:
                                                duplicate_count += 1
                                                self.logger.debug(f"Duplicate pin found by ID at position {scroll_position}px: {pin_id}")
                                                self.logger.debug(f"First seen at position: {seen_pin_ids[pin_id]}px")
                                                continue
                                            
                                            # If it's a new pin, add it to both tracking sets
                                            seen_urls[url] = scroll_position
                                            seen_pin_ids[pin_id] = scroll_position
                                            pin_urls.add(pin_data)
                                            self.logger.debug(f"New pin added: {url} (ID: {pin_id})")
                                    except Exception as e:
                                        self.logger.debug(f"Error processing pin element: {e}")
                                        continue
                                
                                # Check if we found any new pins in this scroll
                                pins_after = len(pin_urls)
                                if pins_after > pins_before:
                                    no_new_pins_count = 0  # Reset counter when we find new pins
                                    self.logger.info(f"Found {pins_after - pins_before} new unique pins")
                                else:
                                    no_new_pins_count += 1
                                    self.logger.info("No new unique pins found in this scroll")
                                
                                # Log progress after each collection
                                self.logger.info(f"Total: {len(pin_urls)} unique pins, {duplicate_count} duplicates")
                                
                                # Check if we've found all expected pins
                                if len(pin_urls) >= expected_pins:
                                    self.logger.info(f"Found all {expected_pins} expected pins!")
                                    break
                                
                                # Random pause (10% chance)
                                if random.random() < self.scroll_pause_chance:
                                    pause_time = random.uniform(self.scroll_pause_min, self.scroll_pause_max)
                                    self.logger.debug(f"Random pause for {pause_time:.1f} seconds")
                                    time.sleep(pause_time)
                                
                                # Regular delay between scrolls (longer in final pass)
                                self.random_delay(self.scroll_delay_min * 1.5, self.scroll_delay_max * 1.5)
                                
                                # Break if we've hit the bottom again
                                if scroll_position >= current_height - 1000:
                                    self.random_delay(2.0, 3.0)
                                    
                                    # Check if more content loaded
                                    new_height = self.driver.execute_script("return document.documentElement.scrollHeight")
                                    if new_height == current_height:
                                        no_change_count += 1
                                    else:
                                        no_change_count = 0
                                        last_content_load_time = current_time
                                        
                                    if no_change_count >= 3 and no_new_pins_count >= 3:
                                        self.logger.info("Reached bottom of page in final pass with no new pins found")
                                        break
                        else:
                            break
        
        self.logger.info(f"\nScroll complete:")
        self.logger.info(f"- Final scroll position: {scroll_position}px")
        self.logger.info(f"- Final page height: {current_height}px")
        self.logger.info(f"- Total unique pins found: {len(pin_urls)}")
        if expected_pins:
            self.logger.info(f"- Expected pins: {expected_pins}")
            if len(pin_urls) < expected_pins:
                self.logger.warning(f"- Missing {expected_pins - len(pin_urls)} pins")
        self.logger.info(f"- Total duplicate pins found: {duplicate_count}")
        self.logger.info(f"- Total scroll time: {elapsed_time:.1f} seconds")
        
        return {
            'final_position': scroll_position,
            'final_height': last_height,
            'reached_bottom': True,
            'pins': pin_urls,
            'duplicate_count': duplicate_count,
            'expected_pins': expected_pins
        }
    
    def extract_pin_data(self, pin_element):
        """Extract data from a pin element."""
        try:
            # Get pin ID from the URL
            pin_link = pin_element.find_element(By.CSS_SELECTOR, 'a[href*="/pin/"]')
            if not pin_link:
                return None
            
            href = pin_link.get_attribute('href')
            pin_id = href.split('/pin/')[1].split('/')[0]
            
            # Get image URL (highest resolution available)
            img = pin_element.find_element(By.CSS_SELECTOR, self.selectors['pin_image'])
            if not img:
                return None
            
            image_url = img.get_attribute('src')
            # Convert to desired size URL
            for size in ['236x', '600x']:
                if size in image_url and size != self.image_size:
                    image_url = image_url.replace(size, self.image_size)
            
            # Get pin title/description
            try:
                title_element = pin_element.find_element(By.CSS_SELECTOR, self.selectors['pin_title'])
                title = title_element.text
            except NoSuchElementException:
                title = ''
            
            # Get board information if organizing by board
            board_name = ''
            if self.organize_by_board:
                try:
                    board_element = pin_element.find_element(By.CSS_SELECTOR, self.selectors['board_name'])
                    board_name = slugify(board_element.text)
                except NoSuchElementException:
                    pass
            
            return {
                'id': pin_id,
                'title': title,
                'image_url': image_url,
                'source_url': f'https://pinterest.com{href}',
                'board': board_name,
                'extracted_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting pin data: {e}")
            return None
    
    def get_image_path(self, pin_data):
        """Generate the appropriate image path based on configuration."""
        if self.organize_by_board and pin_data.get('board'):
            board_dir = self.images_dir / pin_data['board']
            board_dir.mkdir(exist_ok=True)
            base_dir = board_dir
        else:
            base_dir = self.images_dir
        
        title_slug = slugify(pin_data['title'])[:50] if pin_data['title'] else ''
        filename = f"{pin_data['id']}-{title_slug}.jpg" if title_slug else f"{pin_data['id']}.jpg"
        return base_dir / filename
    
    def download_image(self, pin_data):
        """Download an image using requests with retries."""
        if not pin_data.get('image_url'):
            self.logger.warning(f"No image URL for pin {pin_data.get('id', 'unknown')}")
            return None
        
        image_path = self.get_image_path(pin_data)
        if image_path in self.downloaded_images:
            self.logger.info(f"Image already downloaded: {image_path}")
            return str(image_path)
        
        import requests
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        session = requests.Session()
        retries = Retry(total=self.max_retries,
                       backoff_factor=self.retry_delay,
                       status_forcelist=[500, 502, 503, 504])
        session.mount('http://', HTTPAdapter(max_retries=retries))
        session.mount('https://', HTTPAdapter(max_retries=retries))
        
        try:
            # Apply rate limiting
            self.rate_limiter.wait_if_needed()
            
            # Log download attempt
            self.logger.info(f"Downloading image for pin {pin_data['id']}")
            self.logger.info(f"Title: {pin_data.get('title', 'No title')}")
            self.logger.info(f"URL: {pin_data['image_url']}")
            self.logger.info(f"Target path: {image_path}")
            
            # Download image with longer delay
            self.random_delay(self.download_delay_min, self.download_delay_max)
            
            response = session.get(pin_data['image_url'], timeout=self.download_timeout)
            response.raise_for_status()
            
            # Get file size and content type
            file_size = len(response.content)
            content_type = response.headers.get('content-type', 'unknown')
            
            with open(image_path, 'wb') as f:
                f.write(response.content)
            
            self.downloaded_images.add(image_path)
            self.logger.info(f"Successfully downloaded: {image_path}")
            self.logger.info(f"Size: {file_size / 1024:.1f} KB")
            self.logger.info(f"Type: {content_type}")
            
            return str(image_path)
            
        except Exception as e:
            self.logger.error(f"Failed to download image for pin {pin_data['id']}: {e}")
            if self.save_failed_pins:
                self.failed_pins.append(pin_data)
            return None
    
    def get_board_list(self):
        """Get list of boards from profile page."""
        self.logger.info("Getting list of boards...")
        self.driver.get(f'https://pinterest.com/{self.username}/boards/')
        
        # Always start at the top of the page
        self.driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)  # Wait for initial content to load
        
        try:
            WebDriverWait(self.driver, 2).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['board_grid']))
            )
        except TimeoutException:
            self.logger.warning("Board grid not found within 2 seconds, continuing anyway...")
        
        boards = []
        last_height = 0
        scroll_attempts = 0
        max_scroll_attempts = 10  # Maximum number of attempts with no new content
        scroll_position = 0
        
        while True:
            # Get current scroll height
            current_height = self.driver.execute_script("return document.documentElement.scrollHeight")
            
            # Break if we've scrolled all the way (no height change after multiple attempts)
            if current_height == last_height:
                scroll_attempts += 1
                if scroll_attempts >= max_scroll_attempts:
                    break
            else:
                scroll_attempts = 0  # Reset counter when height changes
                last_height = current_height
            
            # Calculate scroll amount based on viewport height with overlap
            overlap_percent = random.uniform(0.1, 0.2)  # 10-20% overlap
            overlap_pixels = int(self.viewport_height * overlap_percent)
            scroll_amount = self.viewport_height - overlap_pixels
            
            # Update scroll position, ensuring we don't go beyond the page height
            new_scroll_position = min(scroll_position + scroll_amount, current_height)
            if new_scroll_position == scroll_position:
                scroll_attempts += 1
            else:
                scroll_position = new_scroll_position
                scroll_attempts = 0
            
            # Scroll smoothly to new position with intermediate steps
            current_pos = self.driver.execute_script("return window.pageYOffset")
            distance = scroll_position - current_pos
            steps = 10  # Number of intermediate steps
            for i in range(1, steps + 1):
                intermediate_pos = current_pos + (distance * i / steps)
                self.driver.execute_script("window.scrollTo(0, arguments[0]);", intermediate_pos)
                time.sleep(0.05)  # Short delay between steps
            
            # Wait for content to load
            time.sleep(1)
            
            # Collect all visible board elements
            board_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-test-id="masonry-container"] [role="listitem"]')
            
            # Process each board element
            for element in board_elements:
                try:
                    link = element.find_element(By.CSS_SELECTOR, 'a')
                    board_url = link.get_attribute('href')
                    board_name = link.text.strip()
                    if not board_name:  # Try getting name from aria-label if text is empty
                        board_name = link.get_attribute('aria-label')
                    
                    # Clean up board name by removing extra whitespace, newlines, and time info
                    parts = board_name.replace('\n', ' ').split(',')
                    board_name = parts[0].strip()  # Get the actual board name
                    
                    # Get pin count from the text
                    pin_count = 0
                    pin_count_text = None
                    for part in parts:
                        part = part.lower().strip()
                        if 'pin' in part:
                            # Extract just the numeric part and handle special cases
                            pin_count_text = part.replace('pins', '').replace('pin', '').strip()
                            try:
                                if 'k' in pin_count_text:
                                    # Handle decimal values with k (e.g., 2.5k = 2500)
                                    k_value = float(pin_count_text.replace('k', ''))
                                    pin_count = int(k_value * 1000)
                                else:
                                    # Remove commas and convert to integer
                                    pin_count = int(pin_count_text.replace(',', ''))
                            except (ValueError, TypeError):
                                # If conversion fails, set pin count to 0 and log the issue
                                pin_count = 0
                                self.logger.debug(f"Could not parse pin count from text: {pin_count_text}")
                            break
                    
                    # Only add board if we haven't seen it before
                    if board_url and board_name and not any(b['url'] == board_url for b in boards):
                        board_data = {
                            'name': board_name,
                            'url': board_url,
                            'slug': slugify(board_name),
                            'pin_count': pin_count,
                            'pin_count_text': pin_count_text if pin_count_text else str(pin_count)
                        }
                        boards.append(board_data)
                except Exception as e:
                    self.logger.debug(f"Error extracting board data: {e}")
                    continue
        
        # Log boards in a clean format
        self.logger.info(f"\nFound {len(boards)} boards:")
        for i, board in enumerate(boards, 1):
            self.logger.info(f"{i}. {board['name']}, {board['pin_count']} Pins")
        
        return boards

    def process_board(self, board_data):
        """Process a single board"""
        # Initialize download tracker at the start
        self.download_tracker = DownloadTracker(self.output_dir)
        
        board_name = board_data['name']
        board_slug = board_data['slug']
        expected_pins = board_data['pin_count']
        
        # Check for existing collection data first
        collections_dir = self.output_dir / 'collections'
        collections_dir.mkdir(exist_ok=True)
        collection_file = collections_dir / f"{board_slug}.json"
        
        self.logger.info(f"Checking for existing collection at: {collection_file}")
        collection_data = None
        pin_urls = []
        existing_pins = {}  # Track existing pins by URL
        
        if collection_file.exists():
            try:
                with open(collection_file, 'r', encoding='utf-8') as f:
                    collection_data = json.load(f)
                    pin_urls = [(pin['url'], pin.get('title', '')) for pin in collection_data['pin_urls']]
                    # Create lookup of existing pins by URL
                    existing_pins = {pin['url']: pin for pin in collection_data['pin_urls']}
                    downloaded_count = sum(1 for pin in collection_data['pin_urls'] if pin.get('downloaded', False))
                    video_count = sum(1 for pin in collection_data['pin_urls'] if pin.get('is_video', False))
                    deleted_count = sum(1 for pin in collection_data['pin_urls'] if pin.get('is_deleted', False))
                    total_processed = downloaded_count + video_count + deleted_count
                    total_pins = len(pin_urls)
                    
                    self.logger.info(f"\nFound existing collection data for {board_name}:")
                    self.logger.info(f"- Total pins in collection: {total_pins}")
                    self.logger.info(f"- Already downloaded: {downloaded_count}")
                    self.logger.info(f"- Video pins: {video_count}")
                    self.logger.info(f"- Deleted/unavailable pins: {deleted_count}")
                    self.logger.info(f"- Total processed: {total_processed}")
                    
                    # Check if we've already processed all pins in the collection
                    if total_processed == total_pins:
                        self.logger.info(f"\nAll {total_pins} pins in collection for board '{board_name}' are already processed:")
                        self.logger.info(f"- Downloaded images: {downloaded_count}")
                        self.logger.info(f"- Video pins: {video_count}")
                        self.logger.info(f"- Deleted/unavailable pins: {deleted_count}")
                        self.download_tracker.mark_board_completed(board_slug)
                        return  # Just return, no need to set goto_pin_processing since we're returning
                    # Check if we've already collected all pins but some still need processing
                    elif total_pins >= expected_pins:
                        self.logger.info(f"\nCollection already has all {expected_pins} pins from board '{board_name}'")
                        self.logger.info("Skipping board crawl and proceeding to process remaining pins.")
                        collection_data = {
                            'board_name': board_name,
                            'board_slug': board_slug,
                            'total_pins': total_pins,
                            'pin_urls': list(existing_pins.values())
                        }
                        goto_pin_processing = True
                    else:
                        goto_pin_processing = False
                        
            except Exception as e:
                self.logger.warning(f"Could not load existing collection data: {e}")
                collection_data = None
                pin_urls = []
                existing_pins = {}
                goto_pin_processing = False
        else:
            self.logger.info(f"No existing collection file found at: {collection_file}")
            goto_pin_processing = False
        
        # Get previously processed pins from download tracker
        previously_processed = {
            pin_id for pin_id in self.tracker.progress['completed_pins']
            if any(pin_id in pin_data.get('board', '') for pin_data in [
                self.tracker.progress['failed_pins'].get(pin_id, {})
            ])
        }
        previously_failed = {
            pin_id for pin_id in self.tracker.progress['failed_pins']
            if any(pin_id in pin_data.get('board', '') for pin_data in [
                self.tracker.progress['failed_pins'].get(pin_id, {})
            ])
        }
        
        self.logger.info(f"\nProcessing board: {board_name}, {expected_pins} Pins")
        if previously_processed or previously_failed:
            self.logger.info(f"Found download progress:")
            if previously_processed:
                self.logger.info(f"- Previously downloaded: {len(previously_processed)} pins")
            if previously_failed:
                self.logger.info(f"- Previously failed: {len(previously_failed)} pins")
        
        self.tracker.set_current_board(board_slug)
        
        try:
            # Only collect pins if we don't already have them all
            if not goto_pin_processing:
                # Navigate to board and collect pins
                self.driver.get(board_data['url'])
                self.wait_for_element(self.selectors['pin_grid'])
                
                # Single scroll session to collect all pins
                scroll_result = self.scroll_to_bottom(start_position=0)
                new_pin_urls = scroll_result['pins']
                
                # Create or update collection data
                if not collection_data:
                    collection_data = {
                        'board_name': board_name,
                        'board_slug': board_slug,
                        'total_pins': len(new_pin_urls),
                        'collection_time': datetime.now().isoformat(),
                        'pin_urls': []
                    }
                
                # Preserve download status for existing pins as a fallback
                existing_status = {}
                if collection_file.exists():
                    try:
                        with open(collection_file, 'r', encoding='utf-8') as f:
                            old_data = json.load(f)
                            for pin in old_data.get('pin_urls', []):
                                existing_status[pin['url']] = {
                                    'downloaded': pin.get('downloaded', False),
                                    'download_time': pin.get('download_time', None),
                                    'is_video': pin.get('is_video', False),
                                    'video_type': pin.get('video_type', None),
                                    'local_image_path': pin.get('local_image_path', None),
                                    'title': pin.get('title', None),
                                    'title_is_empty': pin.get('title_is_empty', False),
                                    'description': pin.get('description', None),
                                    'description_is_empty': pin.get('description_is_empty', False),
                                    'download_failed': pin.get('download_failed', False),
                                    'download_error': pin.get('download_error', None),
                                    'last_checked': pin.get('last_checked', None)
                                }
                    except Exception as e:
                        self.logger.warning(f"Error loading existing pin status: {e}")
                
                # Update collection data with merged pins
                new_pins_added = 0
                updated_pins = 0
                for url, title in new_pin_urls:
                    if url in existing_pins:
                        # Update title if new one is not empty and old one is
                        if title and not existing_pins[url].get('title'):
                            existing_pins[url]['title'] = title
                            updated_pins += 1
                            self.logger.debug(f"Updated title for existing pin: {url}")
                    else:
                        # Add new pin, preserving any existing status
                        new_pins_added += 1
                        status = existing_status.get(url, {})
                        new_pin = {
                            'url': url,
                            'title': title or status.get('title'),
                            'title_is_empty': status.get('title_is_empty', False),
                            'description': status.get('description'),
                            'description_is_empty': status.get('description_is_empty', False),
                            'downloaded': status.get('downloaded', False),
                            'download_time': status.get('download_time', None),
                            'is_video': status.get('is_video', False),
                            'video_type': status.get('video_type', None),
                            'local_image_path': status.get('local_image_path', None),
                            'download_failed': status.get('download_failed', False),
                            'download_error': status.get('download_error', None),
                            'last_checked': status.get('last_checked', None),
                            'added_time': datetime.now().isoformat()
                        }
                        existing_pins[url] = new_pin
                        self.logger.debug(f"Added new pin: {url}")
                
                # Update collection data
                collection_data['pin_urls'] = list(existing_pins.values())
                collection_data['total_pins'] = len(collection_data['pin_urls'])
                collection_data['last_updated'] = datetime.now().isoformat()
                
                # Save updated collection data
                with open(collection_file, 'w', encoding='utf-8') as f:
                    json.dump(collection_data, f, indent=2, ensure_ascii=False)
                
                self.logger.info(f"\nCollection update summary:")
                self.logger.info(f"- Total pins in collection: {len(collection_data['pin_urls'])}")
                self.logger.info(f"- New pins added: {new_pins_added}")
                if updated_pins > 0:
                    self.logger.info(f"- Existing pins updated: {updated_pins}")
            
            # Process pins that need downloading
            pins_to_process = []
            video_pins = []
            already_downloaded = []
            for pin in collection_data['pin_urls']:
                if pin.get('is_video', False):
                    video_pins.append(pin)
                    continue
                if pin.get('downloaded', False):
                    already_downloaded.append(pin)
                    continue
                pin_id = pin['url'].split('/pin/')[1].split('/')[0]
                if pin_id not in previously_processed:
                    pins_to_process.append(pin)
            
            total_pins = len(collection_data['pin_urls'])
            processed_count = len(already_downloaded) + len(video_pins)
            
            self.logger.info(f"\nDownload summary:")
            self.logger.info(f"- Total pins in collection: {total_pins}")
            self.logger.info(f"- Already downloaded: {len(already_downloaded)}")
            self.logger.info(f"- Video pins (skipped): {len(video_pins)}")
            self.logger.info(f"- Total processed: {processed_count}")
            self.logger.info(f"- New pins to download: {len(pins_to_process)}")
            
            # Calculate and show progress percentage
            progress_pct = (processed_count / total_pins) * 100 if total_pins > 0 else 0
            self.logger.info(f"Current progress: {processed_count}/{total_pins} (~{progress_pct:.1f}%)")
            
            # Process each new pin
            successful_downloads = 0
            
            for i, pin_data in enumerate(pins_to_process, 1):
                if self._interrupted:
                    break
                    
                try:
                    pin_url = pin_data['url']
                    pin_id = pin_url.split('/pin/')[1].split('/')[0]
                    
                    # Calculate and show progress including already processed pins
                    current_processed = processed_count + successful_downloads
                    current_pct = (current_processed / total_pins) * 100
                    self.logger.info(f"\nProcessing pin {i}/{len(pins_to_process)} (ID: {pin_id})")
                    self.logger.info(f"Overall progress: {current_processed}/{total_pins} (~{current_pct:.1f}%)")
                    
                    # Navigate to pin page
                    self.driver.get(pin_url)
                    self.random_delay()
                    
                    # Extract pin data
                    pin_info = self.extract_pin_data_from_page(pin_id, board_slug)
                    if pin_info is None:
                        self.logger.warning(f"Could not extract data for pin {pin_id} - pin may have been deleted or made private")
                        # Mark the pin as failed but don't remove it from collection
                        pin_data['download_failed'] = True
                        pin_data['download_error'] = "Could not extract pin data - pin may be deleted or private"
                        pin_data['last_checked'] = datetime.now().isoformat()
                        continue
                        
                    if pin_info.get('is_video', False):
                        self.logger.info(f"Pin {pin_id} is a video pin - marking as video and skipping download")
                        # Update pin status in collection data
                        pin_data['is_video'] = True
                        pin_data['video_type'] = pin_info.get('video_type')
                        video_pins.append(pin_info)
                        processed_count += 1
                    else:
                        # Download image
                        image_path = self.download_image(pin_info)
                        if image_path:
                            pin_info['local_image_path'] = image_path
                            self.tracker.mark_pin_completed(pin_id, board_slug)
                            successful_downloads += 1
                            
                            # Update pin status in collection data
                            pin_data['downloaded'] = True
                            pin_data['download_time'] = datetime.now().isoformat()
                            pin_data['local_image_path'] = image_path
                            pin_data['download_failed'] = False
                            pin_data['download_error'] = None
                        else:
                            pin_data['download_failed'] = True
                            pin_data['download_error'] = "Image download failed"
                            pin_data['last_checked'] = datetime.now().isoformat()
                    
                    # Save collection data after each successful update
                    with open(collection_file, 'w', encoding='utf-8') as f:
                        json.dump(collection_data, f, indent=2, ensure_ascii=False)
                    
                except Exception as e:
                    self.logger.error(f"Error processing pin {pin_id}: {e}")
                    # Record the error in the pin data
                    pin_data['download_failed'] = True
                    pin_data['download_error'] = str(e)
                    pin_data['last_checked'] = datetime.now().isoformat()
                    # Save the error status
                    with open(collection_file, 'w', encoding='utf-8') as f:
                        json.dump(collection_data, f, indent=2, ensure_ascii=False)
                    continue
            
            if self._interrupted:
                self.logger.info("\nPin processing interrupted by user")
                return
            
            # Calculate final progress
            final_processed = processed_count + successful_downloads
            final_pct = (final_processed / total_pins) * 100 if total_pins > 0 else 0
            
            # Log final status
            self.logger.info(f"\nBoard Status:")
            self.logger.info(f"- Total pins found: {total_pins}")
            self.logger.info(f"- Already downloaded: {len(already_downloaded)}")
            self.logger.info(f"- Video pins (skipped): {len(video_pins)}")
            self.logger.info(f"- New downloads: {successful_downloads}")
            self.logger.info(f"- Total processed: {final_processed}")
            self.logger.info(f"- Progress: {final_processed}/{total_pins} (~{final_pct:.1f}%)")
            if not goto_pin_processing and scroll_result['expected_pins']:
                self.logger.info(f"- Expected pins: {scroll_result['expected_pins']}")
            
            self.tracker.mark_board_completed(board_slug)
            self.logger.info(f"Completed board: {board_name}")
            
        except Exception as e:
            self.logger.error(f"Error processing board {board_name}: {e}")
            raise

    def extract_pin_data_from_page(self, pin_id, board_slug):
        """Extract pin data from the pin's page."""
        try:
            # Check for deleted/unavailable pin indicators
            try:
                error_text = self.driver.find_element(By.CSS_SELECTOR, '[data-test-id="page-not-found-error"]').text.lower()
                if 'not available' in error_text or 'page isn\'t available' in error_text:
                    self.logger.info(f"Pin {pin_id} is not available - marking as deleted")
                    return {
                        'id': pin_id,
                        'is_deleted': True,
                        'deleted_reason': 'Pin not available',
                        'board': board_slug,
                        'source_url': self.driver.current_url,
                        'extracted_at': datetime.now().isoformat()
                    }
            except NoSuchElementException:
                pass

            try:
                error_text = self.driver.find_element(By.CSS_SELECTOR, '[data-test-id="error-message"]').text.lower()
                if 'private' in error_text or 'not available' in error_text:
                    self.logger.info(f"Pin {pin_id} is private or not available - marking as deleted")
                    return {
                        'id': pin_id,
                        'is_deleted': True,
                        'deleted_reason': 'Pin is private or not available',
                        'board': board_slug,
                        'source_url': self.driver.current_url,
                        'extracted_at': datetime.now().isoformat()
                    }
            except NoSuchElementException:
                pass

            # First check if this is a video pin
            try:
                video_element = self.driver.find_element(By.CSS_SELECTOR, '[data-test-id="story-pin-video"]')
                if video_element:
                    self.logger.info(f"Pin {pin_id} contains video content - marking as video and skipping")
                    return {
                        'id': pin_id,
                        'is_video': True,
                        'board': board_slug,
                        'source_url': self.driver.current_url,
                        'extracted_at': datetime.now().isoformat()
                    }
            except NoSuchElementException:
                # Not a video pin, continue with image extraction
                pass

            # Try to find any video elements using multiple selectors
            # Be more specific to avoid false positives from ads or UI elements
            video_selectors = [
                '[data-test-id="story-pin-video"]',
                '[data-test-id="video-player"]',
                '[data-test-id="closeup-body-video-container"] video',
                '[data-test-id="visual-content-container"] video',
                'main video',  # Video in main content area
                '.VideoPlayer',
                '.StoryPinVideo'
            ]

            for selector in video_selectors:
                try:
                    video_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if video_elements:
                        # Additional validation - check if video is actually visible and in main content
                        for video_element in video_elements:
                            try:
                                # Check if video is visible and has reasonable dimensions
                                if (video_element.is_displayed() and
                                    video_element.size['width'] > 100 and
                                    video_element.size['height'] > 100):

                                    self.logger.info(f"Pin {pin_id} contains video content (found via {selector}) - marking as video and skipping")
                                    self.logger.debug(f"Video element size: {video_element.size}")
                                    return {
                                        'id': pin_id,
                                        'is_video': True,
                                        'video_type': selector,
                                        'board': board_slug,
                                        'source_url': self.driver.current_url,
                                        'extracted_at': datetime.now().isoformat()
                                    }
                            except Exception as e:
                                self.logger.debug(f"Error checking video element: {e}")
                                continue
                except NoSuchElementException:
                    continue
            
            # Wait for the main image container with multiple selectors
            image_found = False
            image_selectors = [
                self.selectors['pin_image'],  # '[data-test-id="closeup-body-image-container"] img'
                'img[src*="pinimg.com"]',     # Any Pinterest image
                '[data-test-id="pin-image"] img',  # Alternative pin image selector
                '[data-test-id="visual-content-container"] img',  # Visual content container
                'img[alt*="Pin"]',            # Images with "Pin" in alt text
                'main img',                   # Any image in main content
                'img'                         # Fallback to any image
            ]

            for selector in image_selectors:
                try:
                    self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    self.logger.debug(f"Found image using selector: {selector}")
                    image_found = True
                    break
                except TimeoutException:
                    self.logger.debug(f"Image not found with selector: {selector}")
                    continue

            if not image_found:
                # Log page source for debugging
                self.logger.warning(f"No image found for pin {pin_id}. Page title: {self.driver.title}")
                self.logger.debug(f"Current URL: {self.driver.current_url}")

                # Check if page has any images at all
                all_images = self.driver.find_elements(By.TAG_NAME, 'img')
                self.logger.debug(f"Total images on page: {len(all_images)}")

                if all_images:
                    for i, img in enumerate(all_images[:5]):  # Log first 5 images
                        src = img.get_attribute('src') or 'No src'
                        alt = img.get_attribute('alt') or 'No alt'
                        self.logger.debug(f"Image {i+1}: src={src[:100]}..., alt={alt}")

                self.logger.info(f"Pin {pin_id} content not found - marking as deleted")
                return {
                    'id': pin_id,
                    'is_deleted': True,
                    'deleted_reason': 'Pin content not found',
                    'board': board_slug,
                    'source_url': self.driver.current_url,
                    'extracted_at': datetime.now().isoformat()
                }
            
            # Get pin title and description before clicking media viewer
            title = None
            title_is_empty = False
            try:
                # First try to get title from h1 element
                title_element = self.driver.find_element(By.CSS_SELECTOR, 'h1')
                title = title_element.text.strip()
                if not title:  # If h1 is empty, try the pin title selector
                    title_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['pin_title'])
                    title = title_element.text.strip()
                
                if title:
                    self.logger.info(f"Found title: {title}")
                else:
                    self.logger.info("Pin has no title")
                    title_is_empty = True
            except NoSuchElementException:
                self.logger.info("Pin has no title element")
                title_is_empty = True
            
            # Get description from truncated description spans
            description = None
            description_is_empty = False
            try:
                desc_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-test-id="truncated-description"] span')
                description = ' '.join(elem.text.strip() for elem in desc_elements if elem.text.strip())
                
                if description:
                    self.logger.info(f"Found description: {description}")
                else:
                    self.logger.info("Pin has no description")
                    description_is_empty = True
            except NoSuchElementException:
                self.logger.info("Pin has no description element")
                description_is_empty = True
            
            # Click the media viewer button - try multiple selectors
            media_button = None
            media_button_selectors = [
                '[data-test-id="closeup-image-overlay-layer-media-viewer-button"]',
                '[data-test-id="media-viewer-button"]',
                '[data-test-id="closeup-body-image-container"]',  # Sometimes the container itself is clickable
                'img[src*="pinimg.com"]'  # Last resort - click the image itself
            ]

            for selector in media_button_selectors:
                try:
                    media_button = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                    self.logger.debug(f"Found media viewer button using selector: {selector}")
                    break
                except TimeoutException:
                    self.logger.debug(f"Media viewer button not found with selector: {selector}")
                    continue

            if not media_button:
                raise Exception("No media viewer button found with any selector")

            try:
                # Try clicking the media viewer button
                self.driver.execute_script("arguments[0].click();", media_button)
                time.sleep(2)  # Wait for media viewer to open
                
                # Check again for video content in the media viewer with more specific selectors
                media_viewer_video_selectors = [
                    '[role="dialog"] video',  # Video in dialog
                    '[data-test-id="media-viewer"] video',  # Video in media viewer
                    '[data-test-id="story-pin-video"]',
                    '[data-test-id="video-player"]'
                ]

                for selector in media_viewer_video_selectors:
                    try:
                        video_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if video_elements:
                            for video_element in video_elements:
                                try:
                                    # Check if video is visible and has reasonable dimensions
                                    if (video_element.is_displayed() and
                                        video_element.size['width'] > 100 and
                                        video_element.size['height'] > 100):

                                        self.logger.info(f"Pin {pin_id} contains video content in media viewer (found via {selector}) - marking as video and skipping")
                                        self.logger.debug(f"Media viewer video element size: {video_element.size}")
                                        return {
                                            'id': pin_id,
                                            'is_video': True,
                                            'video_type': f"media_viewer_{selector}",
                                            'board': board_slug,
                                            'source_url': self.driver.current_url,
                                            'extracted_at': datetime.now().isoformat()
                                        }
                                except Exception as e:
                                    self.logger.debug(f"Error checking media viewer video element: {e}")
                                    continue
                    except NoSuchElementException:
                        continue
                
                # Wait for the full-size image to load in the media viewer
                # Try multiple selectors for the media viewer image
                media_viewer_image_selectors = [
                    '[role="dialog"] img',  # Standard dialog image
                    '[data-test-id="media-viewer"] img',  # Media viewer image
                    '[data-test-id="closeup-image"] img',  # Closeup image
                    '.media-viewer img',  # Class-based selector
                    'img[src*="pinimg.com"]'  # Any Pinterest image in the viewer
                ]

                full_img = None
                for selector in media_viewer_image_selectors:
                    try:
                        full_img = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                        self.logger.debug(f"Found media viewer image using selector: {selector}")
                        break
                    except TimeoutException:
                        self.logger.debug(f"Media viewer image not found with selector: {selector}")
                        continue

                if not full_img:
                    raise Exception("No image found in media viewer")

                # Get the full-size image URL
                image_url = full_img.get_attribute('src')
                if not image_url:  # Try srcset if src is empty
                    srcset = full_img.get_attribute('srcset')
                    if srcset:
                        # Get the highest resolution image from srcset
                        sources = [s.strip().split(' ') for s in srcset.split(',')]
                        sources = [(s[0], int(s[1].replace('w', ''))) for s in sources if s[1].endswith('w')]
                        if sources:
                            image_url = max(sources, key=lambda x: x[1])[0]

                # Validate that we got a proper Pinterest image URL
                if image_url and 'pinimg.com' in image_url and 'default_75.png' not in image_url:
                    self.logger.info(f"Found full-size image URL: {image_url[:100]}...")
                else:
                    self.logger.warning(f"Media viewer image may not be full-size: {image_url}")
                    # Continue anyway - might still be valid
                
                if not image_url:
                    self.logger.error(f"Could not find full-size image URL for pin {pin_id}")
                    return {
                        'id': pin_id,
                        'is_deleted': True,
                        'deleted_reason': 'No image URL found',
                        'board': board_slug,
                        'source_url': self.driver.current_url,
                        'extracted_at': datetime.now().isoformat()
                    }
                
                # Close the media viewer (optional, as we'll navigate away anyway)
                try:
                    close_button = self.driver.find_element(By.CSS_SELECTOR, '[role="dialog"] [aria-label="close"]')
                    self.driver.execute_script("arguments[0].click();", close_button)
                except:
                    pass
                
            except Exception as e:
                self.logger.warning(f"Could not open media viewer for pin {pin_id}, falling back to standard image: {e}")
                # Fallback to standard image if media viewer fails
                image_url = None

                # Try multiple selectors for fallback image extraction
                fallback_selectors = [
                    self.selectors['pin_image'],  # Original selector
                    'img[src*="pinimg.com"]',     # Any Pinterest image
                    '[data-test-id="pin-image"] img',  # Alternative pin image
                    '[data-test-id="visual-content-container"] img',  # Visual content
                    'main img[src*="pinimg"]',    # Main content Pinterest images
                    'img[alt*="Pin"]',            # Images with Pin in alt
                    'img'                         # Last resort - any image
                ]

                for selector in fallback_selectors:
                    try:
                        img = self.driver.find_element(By.CSS_SELECTOR, selector)
                        image_url = img.get_attribute('src')

                        if not image_url:  # Try srcset if src is empty
                            srcset = img.get_attribute('srcset')
                            if srcset:
                                sources = [s.strip().split(' ') for s in srcset.split(',')]
                                sources = [(s[0], int(s[1].replace('w', ''))) for s in sources if s[1].endswith('w')]
                                if sources:
                                    image_url = max(sources, key=lambda x: x[1])[0]

                        # Check if this is a valid Pinterest image (not default avatar)
                        if (image_url and 'pinimg.com' in image_url and
                            'default_75.png' not in image_url and
                            'default_280.png' not in image_url and
                            'default_600.png' not in image_url):
                            self.logger.info(f"Found fallback image using selector: {selector}")
                            self.logger.info(f"Image URL: {image_url[:100]}...")
                            break
                        else:
                            self.logger.debug(f"Image found with {selector} but not a valid Pinterest pin image: {image_url}")
                            image_url = None

                    except NoSuchElementException:
                        self.logger.debug(f"No image found with fallback selector: {selector}")
                        continue
                    except Exception as fallback_error:
                        self.logger.debug(f"Error with fallback selector {selector}: {fallback_error}")
                        continue

                if not image_url:
                    self.logger.error(f"Could not find any valid Pinterest image for pin {pin_id}")
                    return {
                        'id': pin_id,
                        'is_deleted': True,
                        'deleted_reason': 'No valid Pinterest image found',
                        'board': board_slug,
                        'source_url': self.driver.current_url,
                        'extracted_at': datetime.now().isoformat()
                    }
            
            if not image_url:
                self.logger.error(f"Could not find any image URL for pin {pin_id}")
                return {
                    'id': pin_id,
                    'is_deleted': True,
                    'deleted_reason': 'No image URL found',
                    'board': board_slug,
                    'source_url': self.driver.current_url,
                    'extracted_at': datetime.now().isoformat()
                }
            
            return {
                'id': pin_id,
                'title': title,
                'title_is_empty': title_is_empty,
                'description': description,
                'description_is_empty': description_is_empty,
                'image_url': image_url,
                'source_url': self.driver.current_url,
                'board': board_slug,
                'extracted_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting pin data: {e}")
            return {
                'id': pin_id,
                'is_deleted': True,
                'deleted_reason': f'Error extracting pin data: {str(e)}',
                'board': board_slug,
                'source_url': self.driver.current_url,
                'extracted_at': datetime.now().isoformat()
            }

    def generate_board_report(self, board_slug, processed_pins, stats):
        """Generate an HTML report for a board showing images and their pin URLs."""
        report_dir = self.output_dir / "reports"
        report_dir.mkdir(exist_ok=True)
        report_file = report_dir / f"{board_slug}.html"
        
        # Calculate total successful pins (downloaded + videos)
        total_processed = stats['successful_downloads'] + stats['video_pins']
        
        # Create status message
        if stats['expected_pins'] is not None:
            if stats['total_found'] >= stats['expected_pins']:
                status_color = "#4CAF50"  # Green
                status_icon = "✓"
                status_message = f"All {stats['expected_pins']} pins found"
            else:
                status_color = "#f44336"  # Red
                status_icon = "❌"
                status_message = f"Missing pins (found {stats['total_found']} of {stats['expected_pins']} expected)"
        else:
            status_color = "#ff9800"  # Orange
            status_icon = "⚠️"
            status_message = f"Found {stats['total_found']} pins (total count unknown)"
        
        # Create HTML content
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Pinterest Board: {board_slug}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }}
        h1 {{
            color: #e60023;
            text-align: center;
        }}
        .pin-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }}
        .pin-card {{
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .pin-card img {{
            width: 100%;
            height: auto;
            border-radius: 4px;
            cursor: pointer;
            transition: opacity 0.2s;
            object-fit: cover;
            max-height: 300px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .pin-card img:hover {{
            opacity: 0.9;
        }}
        .pin-card a {{
            display: block;
            margin-top: 10px;
            color: #333;
            text-decoration: none;
            word-break: break-all;
        }}
        .pin-card a:hover {{
            color: #e60023;
        }}
        .pin-title {{
            margin: 10px 0;
            font-weight: bold;
        }}
        .pin-description {{
            margin: 8px 0;
            font-size: 0.9em;
            color: #666;
            line-height: 1.4;
            max-height: 4.2em;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }}
        .stats {{
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .status {{
            color: {status_color};
            font-size: 1.2em;
            font-weight: bold;
            margin: 10px 0;
        }}
        /* Modal styles */
        .modal {{
            display: none;
            position: fixed;
            z-index: 1000;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
            overflow: auto;
        }}
        .modal-content {{
            margin: auto;
            display: block;
            max-width: 90%;
            max-height: 90vh;
            margin-top: 2vh;
        }}
        .modal-close {{
            position: fixed;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }}
        .gallery-item img {{
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .gallery-item .pin-description {{
            margin-top: 8px;
            font-size: 0.9em;
            color: #666;
            line-height: 1.4;
            max-height: 4.2em;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }}
    </style>
</head>
<body>
    <h1>Pinterest Board: {board_slug}</h1>
    <div class="stats">
        <div class="status">{status_icon} {status_message}</div>
        <p>Total Pins in Board: {stats['total_found']}</p>
        <p>Successfully Downloaded: {stats['successful_downloads']}</p>
        <p>Video Pins: {stats['video_pins']}</p>
        <p>Total Processed: {total_processed}</p>
        <p>Images in Gallery: {len(processed_pins)}</p>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    <div class="pin-grid">
"""
        
        # Add each pin to the report
        for pin in processed_pins:
            if pin.get('local_image_path'):
                # Get relative path for the image
                rel_path = os.path.relpath(pin['local_image_path'], report_dir)
                html_content += f"""
    <div class="pin-card">
        <img src="{rel_path}" alt="{pin.get('title', 'Pinterest Pin')}" onclick="showImage('{rel_path}')">
        <div class="pin-title">{pin.get('title', 'Untitled Pin')}</div>
        <div class="pin-description">{pin.get('description', '')}</div>
        <a href="{pin['source_url']}" target="_blank">View on Pinterest</a>
    </div>"""
        
        # Add modal for full-size image viewing
        html_content += """
    </div>
    
    <!-- Modal for full-size images -->
    <div id="imageModal" class="modal" onclick="hideModal()">
        <span class="modal-close">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>

    <script>
        function showImage(imagePath) {
            var modal = document.getElementById('imageModal');
            var modalImg = document.getElementById('modalImage');
            modal.style.display = "block";
            modalImg.src = imagePath;
        }

        function hideModal() {
            var modal = document.getElementById('imageModal');
            modal.style.display = "none";
        }

        // Close modal when pressing escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                hideModal();
            }
        });

        // Prevent modal from closing when clicking the image itself
        document.getElementById('modalImage').onclick = function(event) {
            event.stopPropagation();
        };
    </script>
</body>
</html>
"""
        
        # Write the report
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"Generated HTML report for board {board_slug}: {report_file}")
        return report_file

    def export_pins(self, board_slugs=None, skip_slugs=None):
        """Export all pins using browser automation.
        
        Args:
            board_slugs (list[str], optional): List of board slugs to process.
                                       If None, process all boards.
            skip_slugs (list[str], optional): List of board slugs to skip.
                                      These boards will be completely skipped without processing.
        """
        try:
            # Get list of boards
            boards = self.get_board_list()
            
            if board_slugs:
                # Filter boards by slug
                selected_boards = []
                for slug in board_slugs:
                    matching_boards = [b for b in boards if b['slug'] == slug]
                    if matching_boards:
                        selected_boards.extend(matching_boards)
                    else:
                        self.logger.warning(f"No board found with slug: {slug}")
                
                if not selected_boards:
                    self.logger.error("None of the specified board slugs were found")
                    self.logger.info("\nAvailable boards:")
                    for board in boards:
                        self.logger.info(f"- {board['name']} (slug: {board['slug']})")
                    return
                
                self.logger.info("\nProcessing selected boards:")
                for board in selected_boards:
                    self.logger.info(f"- {board['name']} ({board['pin_count']} pins)")
                boards = selected_boards
                
                # Reset completion status for selected boards
                for board in boards:
                    if board['slug'] in self.tracker.progress['completed_boards']:
                        self.tracker.progress['completed_boards'].remove(board['slug'])
                        self.logger.info(f"Reset completion status for board: {board['name']}")
            
            # Filter out skipped boards
            if skip_slugs:
                original_count = len(boards)
                boards = [b for b in boards if b['slug'] not in skip_slugs]
                skipped_count = original_count - len(boards)
                if skipped_count > 0:
                    self.logger.info(f"\nSkipping {skipped_count} boards:")
                    for slug in skip_slugs:
                        matching_boards = [b for b in boards if b['slug'] == slug]
                        if matching_boards:
                            self.logger.info(f"- {matching_boards[0]['name']} ({matching_boards[0]['pin_count']} pins)")
            
            # Process each board
            for board in boards:
                if self._interrupted:
                    break
                self.process_board(board)
            
            if not self._interrupted:
                self.logger.info("\nExport complete!")
                self.logger.info(f"Processed {len(self.tracker.progress['completed_pins'])} pins successfully")
                self.logger.info(f"Failed pins: {len(self.tracker.progress['failed_pins'])}")
            
        except KeyboardInterrupt:
            self.logger.info("\nExport interrupted by user.")
        except Exception as e:
            self.logger.error(f"Error during export: {e}")
            raise
        
        finally:
            self.cleanup()

    def rescan_pin_metadata(self, collection_file, rescan_titles=True, rescan_descriptions=True):
        """Rescan pin URLs in a collection to populate missing titles and/or descriptions."""
        try:
            with open(collection_file, 'r', encoding='utf-8') as f:
                collection_data = json.load(f)
            
            # Find pins that need rescanning, including already downloaded ones
            pins_to_scan = []
            if rescan_titles and rescan_descriptions:
                pins_to_scan = [pin for pin in collection_data['pin_urls'] 
                              if (not pin.get('title') and not pin.get('title_is_empty', False)) or 
                                 (not pin.get('description') and not pin.get('description_is_empty', False))]
                if not pins_to_scan:
                    self.logger.info(f"No pins need metadata scanning in {collection_file}")
                    return
                self.logger.info(f"\nFound {len(pins_to_scan)} pins needing metadata in {collection_file}")
                self.logger.info(f"Including {sum(1 for p in pins_to_scan if p.get('downloaded', False))} already downloaded pins")
            elif rescan_titles:
                pins_to_scan = [pin for pin in collection_data['pin_urls'] 
                              if not pin.get('title') and not pin.get('title_is_empty', False)]
                if not pins_to_scan:
                    self.logger.info(f"No pins need title scanning in {collection_file}")
                    return
                self.logger.info(f"\nFound {len(pins_to_scan)} pins needing titles in {collection_file}")
                self.logger.info(f"Including {sum(1 for p in pins_to_scan if p.get('downloaded', False))} already downloaded pins")
            elif rescan_descriptions:
                pins_to_scan = [pin for pin in collection_data['pin_urls'] 
                              if not pin.get('description') and not pin.get('description_is_empty', False)]
                if not pins_to_scan:
                    self.logger.info(f"No pins need description scanning in {collection_file}")
                    return
                self.logger.info(f"\nFound {len(pins_to_scan)} pins needing descriptions in {collection_file}")
                self.logger.info(f"Including {sum(1 for p in pins_to_scan if p.get('downloaded', False))} already downloaded pins")
            
            for i, pin in enumerate(pins_to_scan, 1):
                if self._interrupted:
                    break
                
                pin_url = pin['url']
                pin_id = pin_url.split('/pin/')[1].split('/')[0]
                
                self.logger.info(f"\nProcessing pin {i}/{len(pins_to_scan)} (ID: {pin_id})")
                if pin.get('downloaded', False):
                    self.logger.info("This pin was previously downloaded")
                
                try:
                    # Apply rate limiting
                    self.rate_limiter.wait_if_needed()
                    
                    # Navigate to pin page
                    self.driver.get(pin_url)
                    self.random_delay()
                    
                    # Try to get the title if needed
                    if rescan_titles and not pin.get('title') and not pin.get('title_is_empty', False):
                        try:
                            # First try h1 element
                            title_element = self.driver.find_element(By.CSS_SELECTOR, 'h1')
                            title = title_element.text.strip()
                            if not title:  # If h1 is empty, try the pin title selector
                                title_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['pin_title'])
                                title = title_element.text.strip()
                            
                            if title:
                                pin['title'] = title
                                self.logger.info(f"Found title: {title}")
                            else:
                                self.logger.info("Pin has no title")
                                pin['title_is_empty'] = True
                        except NoSuchElementException:
                            self.logger.info("Pin has no title element")
                            pin['title_is_empty'] = True
                    
                    # Try to get the description if needed
                    if rescan_descriptions and not pin.get('description') and not pin.get('description_is_empty', False):
                        try:
                            desc_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-test-id="truncated-description"] span')
                            description = ' '.join(elem.text.strip() for elem in desc_elements if elem.text.strip())
                            
                            if description:
                                pin['description'] = description
                                self.logger.info(f"Found description: {description}")
                            else:
                                self.logger.info("Pin has no description")
                                pin['description_is_empty'] = True
                        except NoSuchElementException:
                            self.logger.info("Pin has no description element")
                            pin['description_is_empty'] = True
                    
                except Exception as e:
                    self.logger.error(f"Error processing pin {pin_id}: {e}")
                    continue
                
                # Save after each successful update
                with open(collection_file, 'w', encoding='utf-8') as f:
                    json.dump(collection_data, f, indent=2, ensure_ascii=False)
            
            # Final summary
            title_stats = {
                'found': sum(1 for p in collection_data['pin_urls'] if p.get('title')),
                'empty': sum(1 for p in collection_data['pin_urls'] if p.get('title_is_empty', False)),
                'unknown': sum(1 for p in collection_data['pin_urls'] if not p.get('title') and not p.get('title_is_empty', False))
            }
            desc_stats = {
                'found': sum(1 for p in collection_data['pin_urls'] if p.get('description')),
                'empty': sum(1 for p in collection_data['pin_urls'] if p.get('description_is_empty', False)),
                'unknown': sum(1 for p in collection_data['pin_urls'] if not p.get('description') and not p.get('description_is_empty', False))
            }
            
            self.logger.info(f"\nMetadata scan complete for {collection_file}")
            self.logger.info("\nTitle statistics:")
            self.logger.info(f"- Found: {title_stats['found']}")
            self.logger.info(f"- Confirmed empty: {title_stats['empty']}")
            self.logger.info(f"- Not yet scanned: {title_stats['unknown']}")
            self.logger.info("\nDescription statistics:")
            self.logger.info(f"- Found: {desc_stats['found']}")
            self.logger.info(f"- Confirmed empty: {desc_stats['empty']}")
            self.logger.info(f"- Not yet scanned: {desc_stats['unknown']}")
            
        except Exception as e:
            self.logger.error(f"Error processing collection file {collection_file}: {e}")

    def generate_stats(self):
        """Generate statistics for all boards from collection files."""
        collections_dir = self.output_dir / 'collections'
        if not collections_dir.exists():
            self.logger.error("No collections directory found")
            return

        collection_files = list(collections_dir.glob('*.json'))
        if not collection_files:
            self.logger.error("No collection files found")
            return

        total_stats = {
            'total_boards': len(collection_files),
            'total_pins': 0,
            'total_downloaded': 0,
            'total_videos': 0,
            'total_missing': 0,
            'boards': []
        }

        # First show detailed stats for each board
        self.logger.info("\nDetailed Board Statistics:")
        self.logger.info("=" * 80)

        for collection_file in sorted(collection_files):
            try:
                with open(collection_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                board_name = data.get('board_name', collection_file.stem)
                total_pins = len(data['pin_urls'])
                downloaded_pins = sum(1 for pin in data['pin_urls'] if pin.get('downloaded', False))
                video_pins = sum(1 for pin in data['pin_urls'] if pin.get('is_video', False))
                missing_pins = sum(1 for pin in data['pin_urls'] 
                                 if not pin.get('is_video', False) and not pin.get('downloaded', False))
                empty_titles = sum(1 for pin in data['pin_urls'] if pin.get('title_is_empty', False))
                empty_descriptions = sum(1 for pin in data['pin_urls'] if pin.get('description_is_empty', False))
                pins_with_titles = sum(1 for pin in data['pin_urls'] if pin.get('title') and not pin.get('title_is_empty', False))
                pins_with_descriptions = sum(1 for pin in data['pin_urls'] if pin.get('description') and not pin.get('description_is_empty', False))
                
                board_stats = {
                    'name': board_name,
                    'total_pins': total_pins,
                    'downloaded_pins': downloaded_pins,
                    'video_pins': video_pins,
                    'missing_pins': missing_pins,
                    'empty_titles': empty_titles,
                    'empty_descriptions': empty_descriptions,
                    'pins_with_titles': pins_with_titles,
                    'pins_with_descriptions': pins_with_descriptions
                }
                
                # Update total stats
                total_stats['total_pins'] += total_pins
                total_stats['total_downloaded'] += downloaded_pins
                total_stats['total_videos'] += video_pins
                total_stats['total_missing'] += missing_pins
                total_stats['boards'].append(board_stats)
                
                # Calculate completion percentage
                completion_pct = (downloaded_pins + video_pins) / total_pins * 100 if total_pins > 0 else 0
                
                # Display detailed board statistics
                self.logger.info(f"\nBoard: {board_name}")
                self.logger.info("-" * 40)
                self.logger.info(f"Total Pins: {total_pins}")
                self.logger.info(f"Downloaded: {downloaded_pins}")
                self.logger.info(f"Video Pins: {video_pins}")
                self.logger.info(f"Missing Images: {missing_pins}")
                self.logger.info(f"Completion: {completion_pct:.1f}%")
                self.logger.info(f"Metadata Status:")
                self.logger.info(f"  - Pins with titles: {pins_with_titles}")
                self.logger.info(f"  - Empty titles: {empty_titles}")
                self.logger.info(f"  - Pins with descriptions: {pins_with_descriptions}")
                self.logger.info(f"  - Empty descriptions: {empty_descriptions}")
                
            except Exception as e:
                self.logger.error(f"Error processing collection file {collection_file}: {e}")
                continue

        # Display overall statistics
        self.logger.info("\nOverall Statistics:")
        self.logger.info("=" * 80)
        self.logger.info(f"Total Boards: {total_stats['total_boards']}")
        self.logger.info(f"Total Pins: {total_stats['total_pins']}")
        self.logger.info(f"Total Downloaded: {total_stats['total_downloaded']}")
        self.logger.info(f"Total Video Pins: {total_stats['total_videos']}")
        self.logger.info(f"Total Missing Images: {total_stats['total_missing']}")
        
        # Calculate overall completion percentage
        overall_completion = ((total_stats['total_downloaded'] + total_stats['total_videos']) / 
                             total_stats['total_pins'] * 100 if total_stats['total_pins'] > 0 else 0)
        self.logger.info(f"Overall Completion: {overall_completion:.1f}%")

        # Now show table summary
        self.logger.info("\nSummary Table:")
        self.logger.info("=" * 80)
        
        # Print table header
        header = [
            "Board Name",
            "Total",
            "Downloaded",
            "Videos",
            "Missing",
            "Progress",
            "With Title",
            "With Desc"
        ]
        
        # Calculate column widths
        widths = [len(h) for h in header]
        for board in total_stats['boards']:
            widths[0] = max(widths[0], len(board['name']))
            widths[1] = max(widths[1], len(str(board['total_pins'])))
            widths[2] = max(widths[2], len(str(board['downloaded_pins'])))
            widths[3] = max(widths[3], len(str(board['video_pins'])))
            widths[4] = max(widths[4], len(str(board['missing_pins'])))
            widths[5] = max(widths[5], len("100.0%"))
            widths[6] = max(widths[6], len(str(board['pins_with_titles'])))
            widths[7] = max(widths[7], len(str(board['pins_with_descriptions'])))

        # Print header
        header_format = "  ".join(f"{{:<{w}}}" for w in widths)
        separator = "-" * (sum(widths) + 2 * (len(widths) - 1))
        self.logger.info(header_format.format(*header))
        self.logger.info(separator)

        # Print board rows
        for board in sorted(total_stats['boards'], key=lambda x: x['name'].lower()):
            completion = (board['downloaded_pins'] + board['video_pins']) / board['total_pins'] * 100 if board['total_pins'] > 0 else 0
            row = [
                board['name'],
                str(board['total_pins']),
                str(board['downloaded_pins']),
                str(board['video_pins']),
                str(board['missing_pins']),
                f"{completion:.1f}%",
                str(board['pins_with_titles']),
                str(board['pins_with_descriptions'])
            ]
            self.logger.info(header_format.format(*row))

        self.logger.info(separator)

        # Print totals
        total_completion = (total_stats['total_downloaded'] + total_stats['total_videos']) / total_stats['total_pins'] * 100 if total_stats['total_pins'] > 0 else 0
        total_with_titles = sum(b['pins_with_titles'] for b in total_stats['boards'])
        total_with_descriptions = sum(b['pins_with_descriptions'] for b in total_stats['boards'])
        totals_row = [
            "TOTAL",
            str(total_stats['total_pins']),
            str(total_stats['total_downloaded']),
            str(total_stats['total_videos']),
            str(total_stats['total_missing']),
            f"{total_completion:.1f}%",
            str(total_with_titles),
            str(total_with_descriptions)
        ]
        self.logger.info(header_format.format(*totals_row))
        self.logger.info("\n")
        
        return total_stats

    def check_missing_images(self):
        """Check for missing images in saved boards and attempt to download them."""
        collections_dir = self.output_dir / 'collections'
        if not collections_dir.exists():
            self.logger.error("No collections directory found")
            return
        
        collection_files = list(collections_dir.glob('*.json'))
        if not collection_files:
            self.logger.error("No collection files found")
            return
            
        total_missing = 0
        total_downloaded = 0
        total_skipped = 0
        skipped_reasons = {
            'video': 0,
            'failed': 0,
            'deleted': 0
        }
        
        # First pass - count total missing images and identify which ones to process
        self.logger.info("\nAnalyzing collections for missing images...")
        pins_to_process = []  # List of (pin, board_name, board_slug, collection_file) tuples
        
        for collection_file in sorted(collection_files):
            try:
                with open(collection_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                board_name = data.get('board_name', collection_file.stem)
                board_slug = data.get('board_slug', slugify(board_name))
                
                for pin in data['pin_urls']:
                    # Skip video pins
                    if pin.get('is_video', False):
                        skipped_reasons['video'] += 1
                        continue
                    
                    # Skip pins marked as failed/deleted
                    if pin.get('download_failed', False):
                        if "deleted" in pin.get('download_error', '').lower() or \
                           "private" in pin.get('download_error', '').lower():
                            skipped_reasons['deleted'] += 1
                        else:
                            skipped_reasons['failed'] += 1
                        continue
                        
                    # Add to processing list if:
                    # 1. Never downloaded before
                    # 2. Was downloaded but file is missing
                    if not pin.get('downloaded', False) or \
                       (pin.get('local_image_path') and not Path(pin.get('local_image_path')).exists()):
                        total_missing += 1
                        pins_to_process.append((pin, board_name, board_slug, collection_file))
                        
            except Exception as e:
                self.logger.error(f"Error analyzing collection file {collection_file}: {e}")
                continue
        
        # Early exit if no missing images found
        if total_missing == 0:
            self.logger.info("No missing images found in any boards")
            return
            
        self.logger.info(f"\nAnalysis complete:")
        self.logger.info(f"Found {total_missing} missing images to process")
        self.logger.info(f"Skipped {sum(skipped_reasons.values())} pins:")
        self.logger.info(f"- Video pins: {skipped_reasons['video']}")
        self.logger.info(f"- Deleted/private pins: {skipped_reasons['deleted']}")
        self.logger.info(f"- Previously failed pins: {skipped_reasons['failed']}")
        
        # Initialize browser only if we have pins to process
        self.logger.info("\nInitializing browser to process missing images...")
        self.setup_driver()
        self.login()
        
        # Process the missing pins
        overall_processed = 0
        current_board = None
        board_processed = 0
        board_total = 0
        
        for pin, board_name, board_slug, collection_file in pins_to_process:
            if self._interrupted:
                break
                
            try:
                # Check if we've moved to a new board
                if current_board != board_name:
                    current_board = board_name
                    board_total = sum(1 for p in pins_to_process if p[1] == board_name)
                    board_processed = 0
                    self.logger.info(f"\nProcessing board: {board_name}")
                    self.logger.info(f"Found {board_total} missing images in this board")
                
                pin_id = pin['url'].split('/pin/')[1].split('/')[0]
                
                # Calculate and display progress
                board_processed += 1
                overall_processed += 1
                board_percent = (board_processed / board_total) * 100
                overall_percent = (overall_processed / total_missing) * 100
                
                self.logger.info(f"\nProcessing pin {pin_id}")
                self.logger.info(f"Board progress: {board_processed}/{board_total} (~{board_percent:.1f}%)")
                self.logger.info(f"Overall progress: {overall_processed}/{total_missing} (~{overall_percent:.1f}%)")
                
                # Navigate to pin page
                self.driver.get(pin['url'])
                self.random_delay()
                
                # Extract pin data
                pin_info = self.extract_pin_data_from_page(pin_id, board_slug)
                if pin_info is None:
                    self.logger.warning(f"Could not extract data for pin {pin_id} - pin may have been deleted or made private")
                    total_skipped += 1
                    skipped_reasons['deleted'] += 1
                    # Mark the pin as failed but don't remove it from collection
                    pin['download_failed'] = True
                    pin['download_error'] = "Could not extract pin data - pin may be deleted or private"
                    pin['last_checked'] = datetime.now().isoformat()
                    continue
                    
                if pin_info.get('is_video', False):
                    pin['is_video'] = True
                    pin['video_type'] = pin_info.get('video_type')
                    total_skipped += 1
                    skipped_reasons['video'] += 1
                    self.logger.info(f"Pin {pin_id} is actually a video pin - marking as video")
                else:
                    # Download image
                    image_path = self.download_image(pin_info)
                    if image_path:
                        pin['local_image_path'] = image_path
                        pin['downloaded'] = True
                        pin['download_time'] = datetime.now().isoformat()
                        pin['download_failed'] = False
                        pin['download_error'] = None
                        total_downloaded += 1
                    else:
                        total_skipped += 1
                        skipped_reasons['failed'] += 1
                        pin['download_failed'] = True
                        pin['download_error'] = "Image download failed"
                        pin['last_checked'] = datetime.now().isoformat()
                            
                # Save after each pin attempt
                with open(collection_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                # Find and update the pin in the data
                for p in data['pin_urls']:
                    if p['url'] == pin['url']:
                        p.update(pin)
                        break
                with open(collection_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                    
            except Exception as e:
                self.logger.error(f"Error processing pin {pin.get('url')}: {e}")
                total_skipped += 1
                skipped_reasons['failed'] += 1
                # Record the error in the pin data
                pin['download_failed'] = True
                pin['download_error'] = str(e)
                pin['last_checked'] = datetime.now().isoformat()
                # Save the error status
                with open(collection_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                # Find and update the pin in the data
                for p in data['pin_urls']:
                    if p['url'] == pin['url']:
                        p.update(pin)
                        break
                with open(collection_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                continue
        
        self.logger.info(f"\nSummary:")
        self.logger.info(f"Total missing images found: {total_missing}")
        self.logger.info(f"Successfully downloaded: {total_downloaded}")
        self.logger.info(f"Total skipped: {total_skipped}")
        self.logger.info(f"\nSkipped breakdown:")
        self.logger.info(f"- Video pins: {skipped_reasons['video']}")
        self.logger.info(f"- Deleted/private pins: {skipped_reasons['deleted']}")
        self.logger.info(f"- Failed downloads: {skipped_reasons['failed']}")
        
        if total_missing > total_downloaded:
            self.logger.info(f"\nReasons for skipped pins:")
            self.logger.info("- Video pins are skipped and marked accordingly")
            self.logger.info("- Deleted or private pins are marked and won't be retried")
            self.logger.info("- Previously failed pins are skipped to avoid repeated failures")
            self.logger.info("\nCheck the JSON collection files for specific error messages")
    
    def report_video_pins(self):
        """Generate a report of all video pins found in collections."""
        collections_dir = self.output_dir / 'collections'
        if not collections_dir.exists():
            self.logger.error("No collections directory found")
            return
            
        collection_files = list(collections_dir.glob('*.json'))
        if not collection_files:
            self.logger.error("No collection files found")
            return
            
        # Create report directory if it doesn't exist
        report_dir = self.output_dir / 'reports'
        report_dir.mkdir(exist_ok=True)
        report_file = report_dir / 'video_pins.html'
        
        total_videos = 0
        video_pins = []
        
        # Collect video pins from all boards
        for collection_file in sorted(collection_files):
            try:
                with open(collection_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                board_name = data.get('board_name', collection_file.stem)
                board_videos = [pin for pin in data['pin_urls'] if pin.get('is_video', False)]
                
                if board_videos:
                    total_videos += len(board_videos)
                    for pin in board_videos:
                        video_pins.append({
                            'board': board_name,
                            'url': pin['url'],
                            'title': pin.get('title', 'Untitled Pin'),
                            'video_type': pin.get('video_type', 'unknown')
                        })
                        
            except Exception as e:
                self.logger.error(f"Error processing collection file {collection_file}: {e}")
                continue
        
        if not video_pins:
            self.logger.info("No video pins found in any collections")
            return
            
        # Generate HTML report
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Pinterest Video Pins Report</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }}
        h1 {{
            color: #e60023;
            text-align: center;
        }}
        .stats {{
            text-align: center;
            margin: 20px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #e60023;
            color: white;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        a {{
            color: #e60023;
            text-decoration: none;
        }}
        a:hover {{
            text-decoration: underline;
        }}
    </style>
</head>
<body>
    <h1>Pinterest Video Pins Report</h1>
    <div class="stats">
        <p>Total Video Pins Found: {total_videos}</p>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    <table>
        <tr>
            <th>Board</th>
            <th>Title</th>
            <th>Video Type</th>
            <th>URL</th>
        </tr>
"""
        
        # Add each video pin to the report
        for pin in sorted(video_pins, key=lambda x: (x['board'], x['url'])):
            html_content += f"""
        <tr>
            <td>{pin['board']}</td>
            <td>{pin['title']}</td>
            <td>{pin['video_type']}</td>
            <td><a href="{pin['url']}" target="_blank">View on Pinterest</a></td>
        </tr>"""
        
        # Close HTML
        html_content += """
    </table>
</body>
</html>
"""
        
        # Write the report
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        self.logger.info(f"\nVideo Pins Summary:")
        self.logger.info(f"Total video pins found: {total_videos}")
        self.logger.info(f"Report generated: {report_file}")
        
        # Also create a simple text file with just the URLs
        urls_file = report_dir / 'video_pins_urls.txt'
        with open(urls_file, 'w', encoding='utf-8') as f:
            for pin in video_pins:
                f.write(f"{pin['url']}\n")
                
        self.logger.info(f"URL list generated: {urls_file}")

    def compare_pin_counts(self):
        """Compare remote (browser) pin counts with local collection data."""
        collections_dir = self.output_dir / 'collections'
        if not collections_dir.exists():
            self.logger.error("No collections directory found")
            return

        self.logger.info("\nComparing remote and local pin counts...")
        
        # First, get remote board data
        self.logger.info("Fetching remote board data...")
        remote_boards = self.get_board_list()
        remote_data = {board['slug']: board for board in remote_boards}
        
        # Now check local collections
        discrepancies = []
        local_only = []
        remote_only = []
        matching = []
        
        collection_files = list(collections_dir.glob('*.json'))
        if not collection_files:
            self.logger.error("No local collection files found")
            return
            
        for collection_file in sorted(collection_files):
            try:
                with open(collection_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                board_name = data.get('board_name', collection_file.stem)
                board_slug = data.get('board_slug', slugify(board_name))
                local_total = len(data['pin_urls'])
                
                if board_slug in remote_data:
                    remote_total = remote_data[board_slug]['pin_count']
                    remote_name = remote_data[board_slug]['name']
                    
                    if local_total != remote_total:
                        discrepancies.append({
                            'slug': board_slug,
                            'name': board_name,
                            'local_total': local_total,
                            'remote_total': remote_total,
                            'difference': remote_total - local_total
                        })
                    else:
                        matching.append({
                            'slug': board_slug,
                            'name': board_name,
                            'total': local_total
                        })
                    
                    # Remove from remote data to track boards only in remote
                    del remote_data[board_slug]
                else:
                    local_only.append({
                        'slug': board_slug,
                        'name': board_name,
                        'total': local_total
                    })
                    
            except Exception as e:
                self.logger.error(f"Error processing collection file {collection_file}: {e}")
                continue
        
        # Any remaining remote boards weren't found locally
        remote_only = [{
            'slug': data['slug'],
            'name': data['name'],
            'total': data['pin_count']
        } for data in remote_data.values()]
        
        # Print terse table format
        self.logger.info("\nPin Count Comparison Report")
        self.logger.info("=" * 80)

        # Combine all boards for table display
        all_boards = []

        # Add discrepancies
        for board in discrepancies:
            all_boards.append({
                'name': board['name'],
                'local': board['local_total'],
                'remote': board['remote_total'],
                'status': '⚠️'
            })

        # Add matching boards
        for board in matching:
            all_boards.append({
                'name': board['name'],
                'local': board['total'],
                'remote': board['total'],
                'status': '✅'
            })

        # Add local-only boards
        for board in local_only:
            all_boards.append({
                'name': board['name'],
                'local': board['total'],
                'remote': 0,
                'status': '📤'
            })

        # Add remote-only boards
        for board in remote_only:
            all_boards.append({
                'name': board['name'],
                'local': 0,
                'remote': board['total'],
                'status': '📥'
            })

        # Sort by name
        all_boards.sort(key=lambda x: x['name'].lower())

        # Calculate column widths
        name_width = max(len('Board Name'), max(len(board['name']) for board in all_boards) if all_boards else 0)
        local_width = max(len('Local'), max(len(str(board['local'])) for board in all_boards) if all_boards else 0)
        remote_width = max(len('Remote'), max(len(str(board['remote'])) for board in all_boards) if all_boards else 0)

        # Print table header
        header = f"{'Board Name':<{name_width}} {'Local':<{local_width}} {'Remote':<{remote_width}} {'%':<6} Status"
        separator = "-" * len(header)
        self.logger.info(f"\n{header}")
        self.logger.info(separator)

        # Print table rows
        for board in all_boards:
            local = board['local']
            remote = board['remote']

            # Calculate percentage
            if remote > 0:
                percentage = (local / remote) * 100
                pct_str = f"{percentage:.1f}%"
            elif local > 0:
                pct_str = "N/A"  # Local exists but no remote
            else:
                pct_str = "0.0%"

            row = f"{board['name']:<{name_width}} {local:<{local_width}} {remote:<{remote_width}} {pct_str:<6} {board['status']}"
            self.logger.info(row)

        self.logger.info(separator)

        # Summary
        self.logger.info("\nSummary:")
        self.logger.info(f"Total boards: {len(all_boards)}")
        self.logger.info(f"✅ Matching counts: {len(matching)}")
        self.logger.info(f"⚠️ Different counts: {len(discrepancies)}")
        self.logger.info(f"📥 Only in remote: {len(remote_only)}")
        self.logger.info(f"📤 Only in local: {len(local_only)}")
        
        return {
            'discrepancies': discrepancies,
            'remote_only': remote_only,
            'local_only': local_only,
            'matching': matching
        }

    def update_boards(self, board_slugs=None, force_update=False):
        """Update local boards with new pins from remote Pinterest boards.

        Args:
            board_slugs (list[str], optional): List of board slugs to update.
                                             If None, updates all boards with discrepancies.
            force_update (bool): If True, forces update even if counts match.
        """
        collections_dir = self.output_dir / 'collections'
        if not collections_dir.exists():
            self.logger.error("No collections directory found")
            return

        self.logger.info("\nUpdating local boards with new remote pins...")

        # First, get comparison data
        comparison_data = self.compare_pin_counts()

        # Determine which boards to update
        boards_to_update = []

        if board_slugs:
            # Update specific boards
            self.logger.info(f"Updating specified boards: {', '.join(board_slugs)}")
            for slug in board_slugs:
                boards_to_update.append(slug)
        else:
            # Update boards with discrepancies or remote-only boards
            for board in comparison_data['discrepancies']:
                if board['difference'] > 0:  # Remote has more pins
                    boards_to_update.append(board['slug'])

            for board in comparison_data['remote_only']:
                boards_to_update.append(board['slug'])

            if force_update:
                # Also update matching boards if forced
                for board in comparison_data['matching']:
                    boards_to_update.append(board['slug'])

        if not boards_to_update:
            self.logger.info("No boards need updating")
            return

        self.logger.info(f"Boards to update: {', '.join(boards_to_update)}")

        # Get remote board data
        remote_boards = self.get_board_list()
        remote_data = {board['slug']: board for board in remote_boards}

        updated_boards = 0
        total_new_pins = 0

        for board_slug in boards_to_update:
            if board_slug not in remote_data:
                self.logger.warning(f"Board '{board_slug}' not found in remote boards, skipping")
                continue

            remote_board = remote_data[board_slug]
            self.logger.info(f"\nUpdating board: {remote_board['name']} ({board_slug})")

            # Check if local collection exists
            collection_file = collections_dir / f"{board_slug}.json"

            if collection_file.exists():
                # Load existing collection
                with open(collection_file, 'r', encoding='utf-8') as f:
                    local_data = json.load(f)

                local_pin_count = len(local_data['pin_urls'])
                remote_pin_count = remote_board['pin_count']

                self.logger.info(f"Local pins: {local_pin_count}, Remote pins: {remote_pin_count}")

                if local_pin_count >= remote_pin_count and not force_update:
                    self.logger.info("Local collection is up to date, skipping")
                    continue
            else:
                # New board - create empty collection
                self.logger.info("Creating new collection for this board")
                local_data = {
                    'board_name': remote_board['name'],
                    'board_slug': board_slug,
                    'board_url': remote_board['url'],
                    'pin_urls': [],
                    'total_pins': 0,
                    'created_at': datetime.now().isoformat(),
                    'last_updated': datetime.now().isoformat()
                }

            # Get existing pin URLs to avoid duplicates
            existing_urls = {pin['url'] for pin in local_data['pin_urls']}

            # Navigate to board and collect pins
            self.logger.info("Collecting pins from remote board...")
            board_url = remote_board['url']
            self.driver.get(board_url)
            self.random_delay()

            # Collect all pins from the board
            scroll_result = self.scroll_to_bottom()
            pin_urls = scroll_result['pins']

            # Filter out existing pins and add new ones (or all if force_update)
            new_pins = []
            pins_to_process = []

            for url, title in pin_urls:
                if url not in existing_urls:
                    # Truly new pin
                    new_pins.append({
                        'url': url,
                        'title': title,
                        'downloaded': False,
                        'local_image_path': None,
                        'board': board_slug,
                        'added_at': datetime.now().isoformat()
                    })
                    pins_to_process.append(new_pins[-1])
                elif force_update:
                    # Existing pin but force update requested
                    existing_pin = next((p for p in local_data['pin_urls'] if p['url'] == url), None)
                    if existing_pin:
                        pins_to_process.append(existing_pin)

            if pins_to_process:
                if new_pins:
                    self.logger.info(f"Found {len(new_pins)} new pins to add")
                    # Add new pins to collection
                    local_data['pin_urls'].extend(new_pins)
                    local_data['total_pins'] = len(local_data['pin_urls'])
                    local_data['last_updated'] = datetime.now().isoformat()

                    # Save updated collection
                    with open(collection_file, 'w', encoding='utf-8') as f:
                        json.dump(local_data, f, indent=2, ensure_ascii=False)

                if force_update:
                    self.logger.info(f"Force update: Processing {len(pins_to_process)} pins (including existing)")
                else:
                    self.logger.info(f"Processing {len(pins_to_process)} new pins")

                # Download images for pins
                downloaded_count = 0

                for i, pin in enumerate(pins_to_process, 1):
                    try:
                        self.logger.info(f"Processing pin {i}/{len(new_pins)}: {pin['url']}")

                        # Navigate to pin page and extract data
                        pin_id = pin['url'].split('/pin/')[1].split('/')[0]
                        pin_data = self.extract_pin_data_from_page(pin_id, board_slug)

                        if pin_data and not pin_data.get('is_video', False):
                            # Download image
                            image_path = self.download_image(pin_data)
                            if image_path:
                                # Update pin in collection
                                pin['downloaded'] = True
                                pin['local_image_path'] = image_path
                                pin['title'] = pin_data.get('title', pin['title'])
                                pin['description'] = pin_data.get('description', '')
                                downloaded_count += 1
                            else:
                                pin['download_failed'] = True
                                pin['download_error'] = "Failed to download image"
                        elif pin_data and pin_data.get('is_video', False):
                            # Mark as video
                            pin['is_video'] = True
                            pin['video_type'] = pin_data.get('video_type', 'unknown')

                        # Save progress after each pin
                        with open(collection_file, 'w', encoding='utf-8') as f:
                            json.dump(local_data, f, indent=2, ensure_ascii=False)

                    except Exception as e:
                        self.logger.error(f"Error processing pin {pin['url']}: {e}")
                        pin['download_failed'] = True
                        pin['download_error'] = str(e)
                        continue

                self.logger.info(f"Board update complete: {downloaded_count}/{len(pins_to_process)} images downloaded")
                total_new_pins += len(new_pins) if new_pins else 0
                updated_boards += 1
            else:
                if force_update:
                    self.logger.info("No pins to process (force update requested but no pins found)")
                else:
                    self.logger.info("No new pins found")

        self.logger.info("\nUpdate summary:")
        self.logger.info(f"Boards updated: {updated_boards}")
        self.logger.info(f"Total new pins added: {total_new_pins}")

    def generate_html_reports(self, board_slugs=None):
        """Generate HTML reports for all boards or update existing ones.
        
        Args:
            board_slugs (list[str], optional): List of board slugs to generate reports for.
                                             If None, generates reports for all boards.
        """
        collections_dir = self.output_dir / 'collections'
        if not collections_dir.exists():
            self.logger.error("No collections directory found")
            return

        collection_files = list(collections_dir.glob('*.json'))
        if not collection_files:
            self.logger.error("No collection files found")
            return

        # Set up reports directory
        report_dir = self.output_dir / 'reports'
        report_dir.mkdir(exist_ok=True)

        # Convert board_slugs to list if provided as comma-separated string
        if isinstance(board_slugs, str):
            board_slugs = [s.strip() for s in board_slugs.split(',')]

        # Clean up reports based on mode
        if not board_slugs:
            # If no specific boards requested, remove all existing reports
            self.logger.info("Cleaning up all existing reports...")
            for report_file in report_dir.glob('*.html'):
                report_file.unlink()
            self.logger.info("Existing reports cleaned up")
        else:
            # If specific boards requested, only remove their reports
            self.logger.info(f"Cleaning up reports for specified boards: {', '.join(board_slugs)}")
            for slug in board_slugs:
                report_file = report_dir / f"{slug}.html"
                if report_file.exists():
                    report_file.unlink()
                    self.logger.info(f"Removed existing report for {slug}")

        self.logger.info("\nGenerating HTML reports for boards...")
        total_reports = 0

        for collection_file in sorted(collection_files):
            try:
                with open(collection_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                board_name = data.get('board_name', collection_file.stem)
                board_slug = data.get('board_slug', slugify(board_name))

                # Skip if not in requested slugs
                if board_slugs and board_slug not in board_slugs:
                    continue
                
                total_pins = len(data['pin_urls'])
                processed_pins = []
                downloaded_count = 0
                video_count = 0
                
                # Prepare pin data for report and count pins
                for pin in data['pin_urls']:
                    # Count downloaded pins and videos separately
                    if pin.get('downloaded', False):
                        downloaded_count += 1
                    if pin.get('is_video', False):
                        video_count += 1
                    
                    # Only add to gallery if we have a local image
                    if pin.get('downloaded', False) and pin.get('local_image_path'):
                        processed_pins.append({
                            'local_image_path': pin['local_image_path'],
                            'title': pin.get('title', 'Untitled Pin'),
                            'description': pin.get('description', ''),
                            'source_url': pin['url']
                        })
                
                # Calculate stats
                stats = {
                    'total_found': total_pins,
                    'successful_downloads': downloaded_count,
                    'video_pins': video_count,
                    'expected_pins': total_pins  # Set expected_pins to total_pins since we have the full count
                }
                
                # Generate report
                report_file = self.generate_board_report(board_slug, processed_pins, stats)
                total_reports += 1
                self.logger.info(f"Generated report for {board_name}: {report_file} ({downloaded_count + video_count}/{total_pins} pins processed)")
                
            except Exception as e:
                self.logger.error(f"Error generating report for {collection_file}: {e}")
                continue
        
        if board_slugs:
            self.logger.info(f"\nGenerated {total_reports} board reports for specified boards: {', '.join(board_slugs)}")
        else:
            self.logger.info(f"\nGenerated {total_reports} board reports for all boards")

    def sync_image_data(self):
        """Scan image folders and update JSON files to reflect downloaded images."""
        collections_dir = self.output_dir / 'collections'
        images_dir = self.output_dir / 'images'
        
        if not collections_dir.exists() or not images_dir.exists():
            self.logger.error("Collections or images directory not found")
            return
            
        self.logger.info("\nScanning image folders and updating JSON data...")
        
        # Process each collection file
        for collection_file in collections_dir.glob('*.json'):
            try:
                board_slug = collection_file.stem
                board_images_dir = images_dir / board_slug
                
                if not board_images_dir.exists():
                    continue
                    
                # Get list of actual image files
                image_files = set()
                for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                    image_files.update(f.name for f in board_images_dir.glob(f'*{ext}'))
                
                if not image_files:
                    continue
                
                # Read collection data
                with open(collection_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                updates_made = 0
                for pin in data['pin_urls']:
                    pin_id = pin['url'].split('/')[-2]  # Extract pin ID from URL
                    
                    # Check for matching image files
                    matching_images = [img for img in image_files if img.startswith(pin_id)]
                    
                    if matching_images:
                        # Update pin data if image exists but not recorded
                        if not pin.get('downloaded') or not pin.get('local_image_path'):
                            pin['downloaded'] = True
                            pin['local_image_path'] = str(board_images_dir / matching_images[0])
                            pin['download_failed'] = False
                            pin['download_error'] = None
                            updates_made += 1
                
                if updates_made > 0:
                    # Save updated collection data
                    with open(collection_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2)
                    self.logger.info(f"Updated {updates_made} pins in {board_slug}")
                
            except Exception as e:
                self.logger.error(f"Error processing {collection_file}: {e}")
                continue
        
        self.logger.info("Image data sync complete")

def main():
    """Main entry point for the Pinterest pin exporter."""
    try:
        # Set up logging first
        log_format = '%(asctime)s - %(levelname)s - %(message)s'

        # Create output directory if it doesn't exist
        output_dir = os.getenv('PINTEREST_OUTPUT_DIR', 'pinterest_export')
        os.makedirs(output_dir, exist_ok=True)
        log_file = os.path.join(output_dir, 'pinterest_export.log')

        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.StreamHandler(sys.stdout),  # Console output
                logging.FileHandler(log_file, encoding='utf-8')  # File output
            ]
        )

        # Create a logger for the main function
        logger = logging.getLogger(__name__)
        logger.info("Pinterest Browser Exporter starting...")

        parser = argparse.ArgumentParser(
            description="""
Pinterest Board Pin Exporter

This script exports pins from Pinterest boards to local storage.
It downloads images and saves pin metadata to JSON files.

Examples:
  # Export all boards
  python pinterest_browser_export.py

  # Export specific boards
  python pinterest_browser_export.py --boards art,design,photography

  # Skip specific boards
  python pinterest_browser_export.py --skip photography

  # Re-crawl a specific board
  python pinterest_browser_export.py --recrawl art-inspiration

  # Generate statistics
  python pinterest_browser_export.py --stats

  # Compare remote and local pin counts
  python pinterest_browser_export.py --compare-counts

  # Generate HTML reports for all boards
  python pinterest_browser_export.py --html-reports

  # Rescan pins to populate missing titles
  python pinterest_browser_export.py --rescan-titles

  # Sync image data with JSONs
  python pinterest_browser_export.py --sync-images

  # Update all boards with new remote pins
  python pinterest_browser_export.py --update-boards all

  # Update specific boards with new remote pins
  python pinterest_browser_export.py --update-boards art,design

  # Force update even if counts match
  python pinterest_browser_export.py --update-boards all --force-update

Board slugs are the URL-friendly versions of board names. For example:
  "Art & Design" -> "art-design"
  "Art & Inspiration" -> "art-inspiration"
"""
        )
        
        # Add existing arguments
        parser.add_argument(
            '--boards', 
            type=str, 
            help='Comma-separated list of board slugs to process (e.g., "art,design"). If not provided, processes all boards.'
        )
        parser.add_argument(
            '--recrawl',
            type=str,
            help='Re-crawl a specific board by slug, updating its existing collection file.'
        )
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Generate statistics about downloaded boards and pins.'
        )
        parser.add_argument(
            '--rescan-titles',
            action='store_true',
            help='Rescan pins to populate missing titles.'
        )
        parser.add_argument(
            '--rescan-descriptions',
            action='store_true',
            help='Rescan pins to populate missing descriptions.'
        )
        parser.add_argument(
            '--check-missing',
            action='store_true',
            help='Check for missing images in collections.'
        )
        parser.add_argument(
            '--video-report',
            action='store_true',
            help='Generate a report of video pins.'
        )
        parser.add_argument(
            '--compare-counts',
            action='store_true',
            help='Compare remote and local pin counts.'
        )
        parser.add_argument(
            '--skip',
            type=str,
            help='Comma-separated list of board slugs to skip during export (e.g., "photography,graphic-design"). These boards will be completely skipped without any processing.'
        )
        parser.add_argument(
            '--html-reports',
            action='store_true',
            help='Generate HTML gallery reports for all downloaded boards.'
        )
        parser.add_argument(
            '--sync-images',
            action='store_true',
            help='Scan image folders and update JSON files to reflect downloaded images.'
        )
        parser.add_argument(
            '--update-boards',
            type=str,
            help='Update local boards with new remote pins. Use "all" to update all boards with discrepancies, or specify comma-separated board slugs (e.g., "art,design").'
        )
        parser.add_argument(
            '--force-update',
            action='store_true',
            help='Force update even if local and remote pin counts match. Only used with --update-boards.'
        )
        args = parser.parse_args()
        logger.info(f"Command line arguments: {vars(args)}")

        # Load configuration from browser-specific env file
        env_file = '.env.browser'
        logger.info(f"Loading configuration from {env_file}")

        if not os.path.exists(env_file):
            logger.error(f"Configuration file {env_file} not found!")
            logger.error("Please create .env.browser with your Pinterest credentials.")
            logger.error("You can copy .env.browser.example as a starting point.")
            sys.exit(1)

        load_dotenv(env_file)
        logger.info("Configuration file loaded successfully")

        # Get configuration from environment
        email = os.getenv('PINTEREST_EMAIL')
        password = os.getenv('PINTEREST_PASSWORD')
        username = os.getenv('PINTEREST_USERNAME')
        output_dir = os.getenv('PINTEREST_OUTPUT_DIR', 'pinterest_export')
        rate_limit_str = os.getenv('MAX_PINS_PER_MINUTE', '0')

        logger.info("Configuration loaded:")
        logger.info(f"  - Email: {'***' if email else 'NOT SET'}")
        logger.info(f"  - Password: {'***' if password else 'NOT SET'}")
        logger.info(f"  - Username: {username or 'NOT SET'}")
        logger.info(f"  - Output directory: {output_dir}")
        logger.info(f"  - Rate limit: {rate_limit_str}")

        try:
            rate_limit = int(rate_limit_str)
        except ValueError:
            logger.error(f"Invalid MAX_PINS_PER_MINUTE value: {rate_limit_str}")
            rate_limit = 0

        # Check for required credentials (except for commands that don't need them)
        commands_without_auth = ['stats', 'html_reports', 'sync_images', 'check_missing', 'video_report']
        commands_without_browser = ['stats', 'html_reports', 'sync_images', 'check_missing', 'video_report']
        needs_auth = not any(getattr(args, cmd, False) for cmd in commands_without_auth)
        needs_browser = not any(getattr(args, cmd, False) for cmd in commands_without_browser)

        if needs_auth and not all([email, password, username]):
            logger.error("Missing required environment variables for authentication!")
            logger.error("Please ensure PINTEREST_EMAIL, PINTEREST_PASSWORD, and PINTEREST_USERNAME are set in .env.browser")
            logger.error("Required for commands that need to access Pinterest (login, crawling, etc.)")
            sys.exit(1)

        # Create exporter instance
        logger.info("Creating Pinterest exporter instance...")
        try:
            exporter = PinterestBrowserExporter(
                email=email,
                password=password,
                username=username,
                output_dir=output_dir,
                rate_limit=rate_limit
            )
            logger.info("Exporter instance created successfully")
        except Exception as e:
            logger.error(f"Failed to create exporter instance: {e}")
            logger.exception("Detailed error:")
            sys.exit(1)
        
        # Process command line arguments
        logger.info("Processing command line arguments...")

        if args.boards:
            board_slugs = [s.strip() for s in args.boards.split(',')]
            logger.info(f"Target boards: {board_slugs}")
        else:
            board_slugs = None
            logger.info("Target boards: ALL")

        if args.skip:
            skip_slugs = [s.strip() for s in args.skip.split(',')]
            logger.info(f"Boards to skip: {skip_slugs}")
        else:
            skip_slugs = None

        # Setup browser only if needed
        if needs_browser:
            logger.info("Setting up browser and logging in...")
            exporter.setup_driver()
            exporter.login()

        # Determine which command to execute
        if args.recrawl:
            logger.info(f"Command: Re-crawl board '{args.recrawl}'")
            exporter.export_pins(board_slugs=[args.recrawl])
        elif args.rescan_titles or args.rescan_descriptions:
            logger.info(f"Command: Rescan metadata (titles: {args.rescan_titles}, descriptions: {args.rescan_descriptions})")
            exporter.rescan_pin_metadata(None, args.rescan_titles, args.rescan_descriptions)
        elif args.stats:
            logger.info("Command: Generate statistics")
            exporter.generate_stats()
        elif args.check_missing:
            logger.info("Command: Check missing images")
            exporter.check_missing_images()
        elif args.video_report:
            logger.info("Command: Generate video pins report")
            exporter.report_video_pins()
        elif args.compare_counts:
            logger.info("Command: Compare remote and local pin counts")
            exporter.compare_pin_counts()
        elif args.html_reports:
            if args.boards:
                board_slugs = [s.strip() for s in args.boards.split(',')]
                logger.info(f"Command: Generate HTML reports for specified boards: {', '.join(board_slugs)}")
                exporter.generate_html_reports(board_slugs=board_slugs)
            else:
                logger.info("Command: Generate HTML reports for all boards")
                exporter.generate_html_reports()
        elif args.sync_images:
            logger.info("Command: Sync image data")
            exporter.sync_image_data()
        elif args.update_boards:
            if args.update_boards.lower() == 'all':
                logger.info("Command: Update all boards with discrepancies")
                update_board_slugs = None
            else:
                update_board_slugs = [s.strip() for s in args.update_boards.split(',')]
                logger.info(f"Command: Update specific boards: {', '.join(update_board_slugs)}")

            logger.info(f"Force update: {args.force_update}")
            exporter.update_boards(board_slugs=update_board_slugs, force_update=args.force_update)
        else:
            logger.info("Command: Full export (default)")
            logger.info(f"  - Boards: {board_slugs or 'ALL'}")
            logger.info(f"  - Skip: {skip_slugs or 'NONE'}")
            exporter.export_pins(board_slugs=board_slugs, skip_slugs=skip_slugs)

        logger.info("Command completed successfully!")
        
    except KeyboardInterrupt:
        print("\nGracefully shutting down...")  # Use print for immediate output
        if 'logger' in locals():
            logger.info("Received keyboard interrupt, shutting down gracefully")
        if 'exporter' in locals():
            exporter.cleanup()
        sys.exit(0)
    except Exception as e:
        if 'logger' in locals():
            logger.error(f"Error during export: {str(e)}")
            logger.exception("Detailed traceback:")
        else:
            print(f"Error during export: {str(e)}")
            import traceback
            traceback.print_exc()

        if 'exporter' in locals():
            try:
                exporter.cleanup()
            except Exception as cleanup_error:
                if 'logger' in locals():
                    logger.error(f"Error during cleanup: {cleanup_error}")
                else:
                    print(f"Error during cleanup: {cleanup_error}")
        sys.exit(1)

if __name__ == '__main__':
    main() 