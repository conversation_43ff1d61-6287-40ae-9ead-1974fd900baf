# Pinterest Browser Exporter - Usage Guide

## Getting Help

The script has built-in help that shows all available options and examples:

```bash
python pinterest_browser_export.py --help
```

This will display:
- Complete description of what the script does
- All available command-line options
- Usage examples for common scenarios
- Information about board slugs

## First Time Setup

1. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Create configuration file**:
   ```bash
   cp .env.browser.example .env.browser
   ```

3. **Edit `.env.browser`** with your Pinterest credentials:
   ```env
   PINTEREST_EMAIL=<EMAIL>
   PINTEREST_PASSWORD=your-password
   PINTEREST_USERNAME=your-username
   ```

4. **Test the setup**:
   ```bash
   python pinterest_browser_export.py --stats
   ```
   (This command doesn't require login, so it's good for testing)

## Basic Usage Patterns

### Full Export (Recommended for first run)
```bash
python pinterest_browser_export.py
```
This will:
- Log into Pinterest
- Discover all your boards
- Download all pins and images
- Save everything to `pinterest_export/`

### Selective Export
```bash
# Export only specific boards
python pinterest_browser_export.py --boards art,design,photography

# Export all except certain boards
python pinterest_browser_export.py --skip private-board,work-stuff
```

### Maintenance Operations
```bash
# Update a specific board with new pins
python pinterest_browser_export.py --recrawl board-name

# Check what's missing and needs re-download
python pinterest_browser_export.py --check-missing
```

### Analysis and Reports
```bash
# See statistics about your collection
python pinterest_browser_export.py --stats

# Generate HTML galleries
python pinterest_browser_export.py --html-reports

# Compare what's on Pinterest vs what you have locally
python pinterest_browser_export.py --compare-counts
```

## Understanding Board Slugs

Board slugs are the URL-friendly names Pinterest uses. You can find them in your board URLs:

```
https://pinterest.com/yourusername/art-and-design/
                                  ^^^^^^^^^^^^^^
                                  This is the board slug
```

Common transformations:
- Spaces become hyphens: "Art Design" → `art-design`
- Special characters are removed: "Art & Design" → `art-design`
- Everything is lowercase: "TRAVEL" → `travel`

## Configuration Tips

### Rate Limiting (Important!)
Start with conservative settings to avoid being blocked:
```env
MAX_PINS_PER_MINUTE=10
ACTION_DELAY_MIN=2.0
ACTION_DELAY_MAX=5.0
```

If you're not getting blocked, you can gradually:
- Increase `MAX_PINS_PER_MINUTE`
- Decrease delay values

### Browser Settings
```env
# For better performance (no visible browser)
BROWSER_HEADLESS=true

# For debugging (see what's happening)
BROWSER_HEADLESS=false
```

### Image Quality
```env
# Best quality (largest files)
PINTEREST_IMAGE_SIZE=original

# Good balance
PINTEREST_IMAGE_SIZE=600x

# Fastest download (smallest files)
PINTEREST_IMAGE_SIZE=236x
```

## Monitoring Progress

### Real-time Logs
```bash
# Watch logs in real-time (Linux/Mac)
tail -f pinterest_export/pinterest_export.log

# On Windows
Get-Content pinterest_export/pinterest_export.log -Wait
```

### Progress Files
The script creates `pinterest_export/download_progress.json` to track:
- Which pins have been downloaded
- Which boards are complete
- Failed downloads for retry

## Resuming Interrupted Downloads

The script automatically resumes where it left off. If interrupted:
1. Just run the same command again
2. It will skip already downloaded content
3. Continue from where it stopped

## Common Workflows

### Initial Setup and Full Export
```bash
# 1. Setup
cp .env.browser.example .env.browser
# Edit .env.browser with your credentials

# 2. Test configuration
python pinterest_browser_export.py --stats

# 3. Full export (run overnight for large collections)
python pinterest_browser_export.py
```

### Regular Maintenance
```bash
# Weekly: Check for new pins
python pinterest_browser_export.py --compare-counts

# Monthly: Full re-scan of active boards
python pinterest_browser_export.py --boards active-board1,active-board2

# As needed: Generate fresh HTML reports
python pinterest_browser_export.py --html-reports
```

### Troubleshooting Workflow
```bash
# 1. Check what's missing
python pinterest_browser_export.py --check-missing

# 2. Re-download specific board
python pinterest_browser_export.py --recrawl problematic-board

# 3. Sync data files
python pinterest_browser_export.py --sync-images

# 4. Generate fresh reports
python pinterest_browser_export.py --html-reports
```

## Performance Expectations

### Typical Speeds
- **Small boards** (< 100 pins): 5-15 minutes
- **Medium boards** (100-1000 pins): 30-90 minutes  
- **Large boards** (1000+ pins): 2-6 hours
- **Full account** (10+ boards): Several hours to days

### Factors Affecting Speed
- **Rate limiting settings**: Lower delays = faster but riskier
- **Internet connection**: Faster connection = faster downloads
- **Pinterest's response**: They may slow down requests
- **Image sizes**: Original quality takes longer
- **Browser mode**: Headless is slightly faster

## Getting Support

1. **Check the logs**: `pinterest_export/pinterest_export.log`
2. **Review configuration**: Ensure `.env.browser` is correct
3. **Try conservative settings**: Increase delays, reduce rate limits
4. **Check documentation**: `README.md`, `DOCUMENTATION.md`
5. **Use built-in help**: `python pinterest_browser_export.py --help`

## Legal and Ethical Use

- **Personal use only**: Don't redistribute downloaded content
- **Respect rate limits**: Don't overload Pinterest's servers
- **Follow Terms of Service**: Pinterest's rules still apply
- **Copyright awareness**: Downloaded content may be copyrighted
